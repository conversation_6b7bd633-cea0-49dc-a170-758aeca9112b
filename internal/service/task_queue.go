package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"code-deploy-service/internal/config"
	"code-deploy-service/internal/types"
	"code-deploy-service/pkg/logger"

	"github.com/redis/go-redis/v9"
)

type TaskQueueManager struct {
	redis     *redis.Client
	config    *config.TaskQueueConfig
	ctx       context.Context
	cancel    context.CancelFunc
	callbacks map[string]func(task *types.DeployTask) error

	// Lua 脚本
	atomicTaskFetchScript   *redis.Script
	atomicLockAcquireScript *redis.Script
}

func NewTaskQueueManager(cfg *config.TaskQueueConfig) (*TaskQueueManager, error) {
	// 验证配置
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 改进的 Redis 配置
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.RedisAddr,
		Password: cfg.RedisPassword,
		DB:       cfg.RedisDB,

		// 连接池配置
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		MaxIdleConns: cfg.MaxIdleConns,

		// 超时配置
		DialTimeout:  time.Duration(cfg.DialTimeout) * time.Second,
		ReadTimeout:  time.Duration(cfg.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.WriteTimeout) * time.Second,
		PoolTimeout:  time.Duration(cfg.PoolTimeout) * time.Second,

		// 重试配置
		MaxRetries:      cfg.MaxRetries,
		MinRetryBackoff: 8 * time.Millisecond,
		MaxRetryBackoff: 512 * time.Millisecond,

		// 连接检查
		ConnMaxIdleTime: 30 * time.Minute,
		ConnMaxLifetime: 5 * time.Minute,
	})

	// 测试 Redis 连接
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		cancel()
		return nil, fmt.Errorf("Redis 连接失败: %v", err)
	}

	logger.Info(ctx, "Redis 连接成功: %s", cfg.RedisAddr)

	// 初始化 Lua 脚本
	atomicTaskFetchScript := redis.NewScript(`
		local queue_key = KEYS[1]
		local instance_tasks_key = KEYS[2]
		local task_key_prefix = KEYS[3]
		local cancel_key_prefix = KEYS[4]
		local instance_id = ARGV[1]
		local max_concurrent = tonumber(ARGV[2])
		local current_time = ARGV[3]

		-- 检查当前实例任务数
		local current_task_count = redis.call('SCARD', instance_tasks_key)
		if current_task_count >= max_concurrent then
			return ""
		end

		-- 循环获取未取消的任务
		for i = 1, 10 do  -- 最多尝试10次，避免无限循环
			local task_id = redis.call('RPOP', queue_key)
			if not task_id then
				return "" -- 队列为空
			end

			-- 检查任务是否存在
			local task_data_key = task_key_prefix .. task_id
			local task_exists = redis.call('EXISTS', task_data_key)
			if task_exists == 0 then
				-- 任务不存在，继续下一个
			else
				-- 检查是否被取消
				local cancel_key = cancel_key_prefix .. task_id
				local is_cancelled = redis.call('EXISTS', cancel_key)
				if is_cancelled == 1 then
					-- 任务已被取消，更新状态
					redis.call('HSET', task_data_key, 'status', 'cancelled', 'updated_at', current_time)
				else
					-- 找到有效任务，原子更新状态
					redis.call('HSET', task_data_key, 
						'status', 'processing',
						'instance_id', instance_id,
						'started_at', current_time,
						'updated_at', current_time
					)
					
					-- 添加到实例任务列表
					redis.call('SADD', instance_tasks_key, task_id)
					
					return task_id
				end
			end
		end
		
		return ""  -- 尝试次数用完
	`)

	atomicLockAcquireScript := redis.NewScript(`
		local lock_key = KEYS[1]
		local cancel_key_prefix = KEYS[2]
		local new_task_id = ARGV[1]
		local lock_expire = tonumber(ARGV[2])
		local cancel_reason = ARGV[3]
		local current_time = ARGV[4]

		-- 尝试获取当前锁持有者
		local existing_task_id = redis.call('GET', lock_key)

		-- 如果有冲突任务且不是当前任务
		if existing_task_id and existing_task_id ~= new_task_id then
			-- 发送取消信号给旧任务
			local cancel_key = cancel_key_prefix .. existing_task_id
			redis.call('HSET', cancel_key, 
				'reason', cancel_reason, 
				'timestamp', current_time
			)
			redis.call('EXPIRE', cancel_key, 60)
		end

		-- 设置新锁
		redis.call('SETEX', lock_key, lock_expire, new_task_id)

		return existing_task_id or ""
	`)

	return &TaskQueueManager{
		redis:                   rdb,
		config:                  cfg,
		ctx:                     ctx,
		cancel:                  cancel,
		callbacks:               make(map[string]func(task *types.DeployTask) error),
		atomicTaskFetchScript:   atomicTaskFetchScript,
		atomicLockAcquireScript: atomicLockAcquireScript,
	}, nil
}

func (tqm *TaskQueueManager) Close() {
	tqm.CloseWithInstance("")
}

func (tqm *TaskQueueManager) CloseWithInstance(instanceID string) {
	// 如果提供了 instanceID，则先注销实例
	if instanceID != "" {
		ctx := context.Background()
		err := tqm.UnregisterInstance(ctx, instanceID)
		if err != nil {
			logger.Error(ctx, "关闭时注销实例失败: instanceID=%s, error=%v", instanceID, err)
		}
	}

	tqm.cancel()
	if tqm.redis != nil {
		tqm.redis.Close()
	}
}

// Redis key 生成函数
func (tqm *TaskQueueManager) getKey(suffix string) string {
	return tqm.config.KeyPrefix + suffix
}

func (tqm *TaskQueueManager) getTaskKey(taskID string) string {
	return tqm.getKey("task:" + taskID)
}

func (tqm *TaskQueueManager) getQueueKey() string {
	return tqm.getKey("queue:pending")
}

func (tqm *TaskQueueManager) getProcessingKey(instanceID string) string {
	return tqm.getKey("queue:processing:" + instanceID)
}

func (tqm *TaskQueueManager) getLockKey(deploymentID string) string {
	return tqm.getKey("lock:" + deploymentID)
}

func (tqm *TaskQueueManager) getInstanceKey(instanceID string) string {
	return tqm.getKey("instance:" + instanceID)
}

func (tqm *TaskQueueManager) getCancelKey(taskID string) string {
	return tqm.getKey("cancel:" + taskID)
}

// SubmitTask 提交新任务（使用原子锁获取）
func (tqm *TaskQueueManager) SubmitTask(ctx context.Context, task *types.DeployTask) error {
	logger.Info(ctx, "提交任务: taskID=%s, deploymentID=%s", task.TaskID, task.DeploymentID)

	// 使用原子锁获取脚本处理冲突
	lockKeys := []string{
		tqm.getLockKey(task.DeploymentID),
		tqm.getKey("cancel:"),
	}

	lockArgs := []interface{}{
		task.TaskID,
		tqm.config.LockExpireTime,
		"新任务提交，取消旧任务",
		time.Now().Format(time.RFC3339),
	}

	// 执行原子锁获取脚本
	oldTaskID, err := tqm.atomicLockAcquireScript.Run(ctx, tqm.redis, lockKeys, lockArgs...).Result()
	if err != nil {
		return fmt.Errorf("获取部署锁失败: %v", err)
	}

	if oldTaskID != nil && oldTaskID.(string) != "" && oldTaskID.(string) != task.TaskID {
		logger.Info(ctx, "取消冲突任务: oldTaskID=%s, newTaskID=%s", oldTaskID.(string), task.TaskID)
	}

	// 序列化任务
	taskData, err := json.Marshal(task)
	if err != nil {
		// 如果序列化失败，需要回滚锁
		tqm.redis.Del(ctx, tqm.getLockKey(task.DeploymentID))
		return fmt.Errorf("序列化任务失败: %v", err)
	}

	// 使用事务存储任务和加入队列
	pipe := tqm.redis.TxPipeline()

	// 存储任务详情
	pipe.HMSet(ctx, tqm.getTaskKey(task.TaskID), map[string]interface{}{
		"data":       string(taskData),
		"status":     string(task.Status),
		"created_at": task.CreatedAt.Format(time.RFC3339),
		"updated_at": time.Now().Format(time.RFC3339),
	})

	// 添加到队列
	pipe.LPush(ctx, tqm.getQueueKey(), task.TaskID)

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		// 如果事务失败，回滚锁
		tqm.redis.Del(ctx, tqm.getLockKey(task.DeploymentID))
		return fmt.Errorf("提交任务事务失败: %v", err)
	}

	logger.Info(ctx, "任务提交成功: taskID=%s", task.TaskID)
	return nil
}

// GetNextTask 获取下一个待处理任务（使用原子操作）
func (tqm *TaskQueueManager) GetNextTask(ctx context.Context, instanceID string) (*types.DeployTask, error) {
	keys := []string{
		tqm.getQueueKey(),
		tqm.getInstanceKey(instanceID) + ":tasks",
		tqm.getKey("task:"),
		tqm.getKey("cancel:"),
	}

	args := []interface{}{
		instanceID,
		tqm.config.MaxConcurrentTasks,
		time.Now().Format(time.RFC3339),
	}

	result, err := tqm.atomicTaskFetchScript.Run(ctx, tqm.redis, keys, args...).Result()
	if err != nil {
		return nil, fmt.Errorf("执行原子获取任务脚本失败: %v", err)
	}

	if result == nil || result.(string) == "" {
		return nil, nil // 没有任务或达到上限
	}

	taskID := result.(string)

	// 获取任务详情
	task, err := tqm.GetTask(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务详情失败: %v", err)
	}

	if task == nil {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	// 更新本地任务信息
	now := time.Now()
	task.Status = types.TaskStatusProcessing
	task.InstanceID = instanceID
	task.StartedAt = &now

	logger.Info(ctx, "原子获取任务成功: taskID=%s, instanceID=%s", taskID, instanceID)
	return task, nil
}

// GetTask 获取任务详情
func (tqm *TaskQueueManager) GetTask(ctx context.Context, taskID string) (*types.DeployTask, error) {
	taskKey := tqm.getTaskKey(taskID)
	taskData, err := tqm.redis.HGetAll(ctx, taskKey).Result()
	if err != nil {
		return nil, fmt.Errorf("获取任务数据失败: %v", err)
	}

	if len(taskData) == 0 {
		return nil, nil
	}

	data, exists := taskData["data"]
	if !exists {
		return nil, fmt.Errorf("任务数据不存在: %s", taskID)
	}

	var task types.DeployTask
	err = json.Unmarshal([]byte(data), &task)
	if err != nil {
		return nil, fmt.Errorf("反序列化任务失败: %v", err)
	}

	// 从Redis中更新最新状态
	if status, exists := taskData["status"]; exists {
		task.Status = types.TaskStatus(status)
	}
	if message, exists := taskData["message"]; exists {
		task.Message = message
	}
	if instanceID, exists := taskData["instance_id"]; exists {
		task.InstanceID = instanceID
	}
	if startedAt, exists := taskData["started_at"]; exists && startedAt != "" {
		if t, err := time.Parse(time.RFC3339, startedAt); err == nil {
			task.StartedAt = &t
		}
	}
	if completedAt, exists := taskData["completed_at"]; exists && completedAt != "" {
		if t, err := time.Parse(time.RFC3339, completedAt); err == nil {
			task.CompletedAt = &t
		}
	}

	return &task, nil
}

// UpdateTaskStatus 更新任务状态
func (tqm *TaskQueueManager) UpdateTaskStatus(ctx context.Context, taskID string, status types.TaskStatus, message string) error {
	return tqm.updateTaskStatus(ctx, taskID, status, message)
}

func (tqm *TaskQueueManager) updateTaskStatus(ctx context.Context, taskID string, status types.TaskStatus, message string) error {
	updates := map[string]interface{}{
		"status":     string(status),
		"message":    message,
		"updated_at": time.Now().Format(time.RFC3339),
	}

	if status == types.TaskStatusCompleted || status == types.TaskStatusFailed || status == types.TaskStatusCancelled {
		updates["completed_at"] = time.Now().Format(time.RFC3339)
	}

	err := tqm.redis.HMSet(ctx, tqm.getTaskKey(taskID), updates).Err()
	if err != nil {
		return fmt.Errorf("更新任务状态失败: %v", err)
	}

	logger.Info(ctx, "任务状态已更新: taskID=%s, status=%s, message=%s", taskID, status, message)
	return nil
}

func (tqm *TaskQueueManager) updateTaskStatusWithDetails(ctx context.Context, task *types.DeployTask) error {
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务失败: %v", err)
	}

	updates := map[string]interface{}{
		"data":       string(taskData),
		"status":     string(task.Status),
		"updated_at": time.Now().Format(time.RFC3339),
	}

	if task.StartedAt != nil {
		updates["started_at"] = task.StartedAt.Format(time.RFC3339)
	}

	if task.CompletedAt != nil {
		updates["completed_at"] = task.CompletedAt.Format(time.RFC3339)
	}

	return tqm.redis.HMSet(ctx, tqm.getTaskKey(task.TaskID), updates).Err()
}

// CompleteTask 完成任务（带回滚机制）
func (tqm *TaskQueueManager) CompleteTask(ctx context.Context, taskID string, instanceID string, success bool, message string) error {
	var status types.TaskStatus
	if success {
		status = types.TaskStatusCompleted
	} else {
		status = types.TaskStatusFailed
	}

	// 获取任务详情以获取 deploymentID
	task, err := tqm.GetTask(ctx, taskID)
	if err != nil {
		logger.Error(ctx, "获取任务详情失败，但继续完成流程: taskID=%s, error=%v", taskID, err)
	}

	// 使用事务确保原子性
	pipe := tqm.redis.TxPipeline()

	// 更新任务状态
	updates := map[string]interface{}{
		"status":       string(status),
		"message":      message,
		"updated_at":   time.Now().Format(time.RFC3339),
		"completed_at": time.Now().Format(time.RFC3339),
	}
	pipe.HMSet(ctx, tqm.getTaskKey(taskID), updates)

	// 从实例任务列表中移除
	pipe.SRem(ctx, tqm.getInstanceKey(instanceID)+":tasks", taskID)

	// 如果有任务详情，释放部署锁
	if task != nil {
		pipe.Del(ctx, tqm.getLockKey(task.DeploymentID))
	}

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		logger.Error(ctx, "完成任务事务失败: taskID=%s, error=%v", taskID, err)
		return fmt.Errorf("完成任务事务失败: %v", err)
	}

	logger.Info(ctx, "任务完成: taskID=%s, success=%v, message=%s", taskID, success, message)
	return nil
}

// CancelTask 取消任务
func (tqm *TaskQueueManager) CancelTask(ctx context.Context, taskID string, reason string) error {
	return tqm.sendCancelSignal(ctx, taskID, reason)
}

func (tqm *TaskQueueManager) sendCancelSignal(ctx context.Context, taskID string, reason string) error {
	cancelData := map[string]interface{}{
		"reason":    reason,
		"timestamp": time.Now().Format(time.RFC3339),
	}

	err := tqm.redis.HMSet(ctx, tqm.getCancelKey(taskID), cancelData).Err()
	if err != nil {
		return fmt.Errorf("发送取消信号失败: %v", err)
	}

	// 设置过期时间
	tqm.redis.Expire(ctx, tqm.getCancelKey(taskID), 60*time.Second)

	logger.Info(ctx, "发送取消信号: taskID=%s, reason=%s", taskID, reason)
	return nil
}

// IsTaskCancelled 检查任务是否被取消
func (tqm *TaskQueueManager) IsTaskCancelled(ctx context.Context, taskID string) bool {
	return tqm.isTaskCancelled(ctx, taskID)
}

func (tqm *TaskQueueManager) isTaskCancelled(ctx context.Context, taskID string) bool {
	exists, err := tqm.redis.Exists(ctx, tqm.getCancelKey(taskID)).Result()
	if err != nil {
		logger.Error(ctx, "检查取消信号失败: %v", err)
		return false
	}
	return exists > 0
}

// 实例管理相关方法

func (tqm *TaskQueueManager) getInstanceCurrentTasks(ctx context.Context, instanceID string) (int, error) {
	count, err := tqm.redis.SCard(ctx, tqm.getInstanceKey(instanceID)+":tasks").Result()
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

func (tqm *TaskQueueManager) addTaskToInstance(ctx context.Context, instanceID string, taskID string) error {
	return tqm.redis.SAdd(ctx, tqm.getInstanceKey(instanceID)+":tasks", taskID).Err()
}

func (tqm *TaskQueueManager) removeTaskFromInstance(ctx context.Context, instanceID string, taskID string) error {
	return tqm.redis.SRem(ctx, tqm.getInstanceKey(instanceID)+":tasks", taskID).Err()
}

// RegisterInstance 注册实例（K8s环境下每次都是新实例）
func (tqm *TaskQueueManager) RegisterInstance(ctx context.Context, instanceID string) error {
	now := time.Now().Format(time.RFC3339)
	instanceData := map[string]interface{}{
		"instance_id":          instanceID,
		"max_concurrent_tasks": tqm.config.MaxConcurrentTasks,
		"registered_at":        now,
		"last_heartbeat":       now,
		"status":               "active", // K8s环境下只有活跃状态
	}

	err := tqm.redis.HMSet(ctx, tqm.getInstanceKey(instanceID), instanceData).Err()
	if err != nil {
		return fmt.Errorf("注册实例失败: %v", err)
	}

	logger.Info(ctx, "K8s实例注册成功: instanceID=%s", instanceID)
	return nil
}

// UpdateHeartbeat 更新实例心跳
func (tqm *TaskQueueManager) UpdateHeartbeat(ctx context.Context, instanceID string) error {
	err := tqm.redis.HSet(ctx, tqm.getInstanceKey(instanceID), "last_heartbeat", time.Now().Format(time.RFC3339)).Err()
	if err != nil {
		return fmt.Errorf("更新心跳失败: %v", err)
	}
	return nil
}

// UnregisterInstance 注销实例（K8s环境下直接删除）
func (tqm *TaskQueueManager) UnregisterInstance(ctx context.Context, instanceID string) error {
	instanceKey := tqm.getInstanceKey(instanceID)
	taskKey := instanceKey + ":tasks"

	// 使用事务一次性删除实例相关的所有数据
	pipe := tqm.redis.TxPipeline()
	pipe.Del(ctx, instanceKey) // 删除实例信息
	pipe.Del(ctx, taskKey)     // 删除实例任务列表

	_, err := pipe.Exec(ctx)
	if err != nil {
		logger.Error(ctx, "删除实例数据失败: instanceID=%s, error=%v", instanceID, err)
		return fmt.Errorf("删除实例数据失败: %v", err)
	}

	logger.Info(ctx, "实例注销成功（已删除）: instanceID=%s", instanceID)
	return nil
}

// CleanupExpiredInstances 清理过期实例（K8s环境下直接删除）
func (tqm *TaskQueueManager) CleanupExpiredInstances(ctx context.Context) error {
	pattern := tqm.getKey("instance:*")
	keys, err := tqm.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("获取实例键失败: %v", err)
	}

	expiredInstances := []string{}
	heartbeatTimeout := time.Duration(tqm.config.InstanceTimeout) * time.Second

	for _, key := range keys {
		// 跳过任务列表键
		if strings.HasSuffix(key, ":tasks") {
			continue
		}

		instanceData, err := tqm.redis.HGetAll(ctx, key).Result()
		if err != nil {
			logger.Error(ctx, "获取实例信息失败: key=%s, error=%v", key, err)
			continue
		}

		instanceID := instanceData["instance_id"]
		if instanceID == "" {
			logger.Warn(ctx, "实例ID为空，直接删除: key=%s", key)
			tqm.redis.Del(ctx, key)
			continue
		}

		lastHeartbeatStr, exists := instanceData["last_heartbeat"]
		if !exists {
			logger.Warn(ctx, "K8s实例缺少心跳信息，直接删除: instanceID=%s", instanceID)
			expiredInstances = append(expiredInstances, instanceID)
			continue
		}

		lastHeartbeat, err := time.Parse(time.RFC3339, lastHeartbeatStr)
		if err != nil {
			logger.Error(ctx, "解析心跳时间失败，直接删除: instanceID=%s, time=%s, error=%v", instanceID, lastHeartbeatStr, err)
			expiredInstances = append(expiredInstances, instanceID)
			continue
		}

		// 检查是否超时
		if time.Since(lastHeartbeat) > heartbeatTimeout {
			logger.Info(ctx, "K8s实例过期，直接删除: instanceID=%s, lastHeartbeat=%s, 超时=%v", instanceID, lastHeartbeatStr, heartbeatTimeout)
			expiredInstances = append(expiredInstances, instanceID)
		}
	}

	// 直接删除过期实例
	deletedCount := 0
	for _, instanceID := range expiredInstances {
		if instanceID == "" {
			continue
		}
		err := tqm.cleanupExpiredInstance(ctx, instanceID)
		if err != nil {
			logger.Error(ctx, "删除过期K8s实例失败: instanceID=%s, error=%v", instanceID, err)
		} else {
			deletedCount++
		}
	}

	if deletedCount > 0 {
		logger.Info(ctx, "K8s环境删除了 %d 个过期实例", deletedCount)
	}

	return nil
}

// cleanupExpiredInstance 清理单个过期实例（K8s环境下直接删除）
func (tqm *TaskQueueManager) cleanupExpiredInstance(ctx context.Context, instanceID string) error {
	// 获取该实例的所有任务
	instanceTasksKey := tqm.getInstanceKey(instanceID) + ":tasks"
	tasks, err := tqm.redis.SMembers(ctx, instanceTasksKey).Result()
	if err != nil {
		logger.Error(ctx, "获取实例任务列表失败: instanceID=%s, error=%v", instanceID, err)
	}

	// 将实例的任务重新放回队列
	if len(tasks) > 0 {
		pipe := tqm.redis.TxPipeline()
		for _, taskID := range tasks {
			// 重置任务状态为待处理
			pipe.HMSet(ctx, tqm.getTaskKey(taskID), map[string]interface{}{
				"status":      string(types.TaskStatusPending),
				"instance_id": "",
				"updated_at":  time.Now().Format(time.RFC3339),
				"message":     "K8s实例过期，任务重新排队",
			})
			// 重新加入队列
			pipe.LPush(ctx, tqm.getQueueKey(), taskID)
		}
		_, err = pipe.Exec(ctx)
		if err != nil {
			logger.Error(ctx, "重新排队任务失败: instanceID=%s, error=%v", instanceID, err)
		} else {
			logger.Info(ctx, "K8s实例过期，重新排队 %d 个任务: instanceID=%s", len(tasks), instanceID)
		}
	}

	// 直接删除实例（K8s环境下不需要离线状态）
	return tqm.UnregisterInstance(ctx, instanceID)
}

// ListTasks 获取任务列表
func (tqm *TaskQueueManager) ListTasks(ctx context.Context, req *types.TaskListRequest) ([]types.DeployTask, int, error) {
	// 获取所有任务键
	pattern := tqm.getKey("task:*")
	keys, err := tqm.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, 0, fmt.Errorf("获取任务键失败: %v", err)
	}

	var tasks []types.DeployTask
	for _, key := range keys {
		taskData, err := tqm.redis.HGet(ctx, key, "data").Result()
		if err != nil {
			continue
		}

		var task types.DeployTask
		if err := json.Unmarshal([]byte(taskData), &task); err != nil {
			continue
		}

		// 应用过滤条件
		if req.Status != "" && task.Status != req.Status {
			continue
		}
		if req.DeploymentID != "" && task.DeploymentID != req.DeploymentID {
			continue
		}
		if req.UserID != "" && task.UserInfo.ID != req.UserID {
			continue
		}
		if req.InstanceID != "" && task.InstanceID != req.InstanceID {
			continue
		}

		tasks = append(tasks, task)
	}

	// 按创建时间排序（最新的在前面）
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].CreatedAt.After(tasks[j].CreatedAt)
	})

	total := len(tasks)

	// 分页
	if req.Limit > 0 {
		start := req.Offset
		end := start + req.Limit
		if start > len(tasks) {
			tasks = []types.DeployTask{}
		} else if end > len(tasks) {
			tasks = tasks[start:]
		} else {
			tasks = tasks[start:end]
		}
	}

	return tasks, total, nil
}

// GetQueueStats 获取队列统计信息
func (tqm *TaskQueueManager) GetQueueStats(ctx context.Context) (*types.QueueStatsResponse, error) {
	stats := &types.QueueStatsResponse{
		Success: true,
		Message: "获取队列统计成功",
	}

	// 统计各状态任务数量
	pattern := tqm.getKey("task:*")
	keys, err := tqm.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("获取任务键失败: %v", err)
	}

	for _, key := range keys {
		status, err := tqm.redis.HGet(ctx, key, "status").Result()
		if err != nil {
			continue
		}

		switch types.TaskStatus(status) {
		case types.TaskStatusPending:
			stats.PendingTasks++
		case types.TaskStatusProcessing:
			stats.ProcessingTasks++
		case types.TaskStatusCompleted:
			stats.CompletedTasks++
		case types.TaskStatusFailed:
			stats.FailedTasks++
		case types.TaskStatusCancelled:
			stats.CancelledTasks++
		}
	}

	// 统计实例信息
	instancePattern := tqm.getKey("instance:*")
	instanceKeys, err := tqm.redis.Keys(ctx, instancePattern).Result()
	if err == nil {
		stats.ActiveInstances = len(instanceKeys)
		stats.TotalCapacity = len(instanceKeys) * tqm.config.MaxConcurrentTasks
		stats.UsedCapacity = stats.ProcessingTasks
	}

	return stats, nil
}

// ListInstances 获取实例列表（K8s环境下只返回活跃实例）
func (tqm *TaskQueueManager) ListInstances(ctx context.Context) ([]types.InstanceInfo, error) {
	// 先清理过期实例
	err := tqm.CleanupExpiredInstances(ctx)
	if err != nil {
		logger.Error(ctx, "清理过期实例失败: %v", err)
	}

	pattern := tqm.getKey("instance:*")
	keys, err := tqm.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("获取实例键失败: %v", err)
	}

	var instances []types.InstanceInfo
	heartbeatTimeout := time.Duration(tqm.config.InstanceTimeout) * time.Second

	for _, key := range keys {
		instanceData, err := tqm.redis.HGetAll(ctx, key).Result()
		if err != nil {
			continue
		}

		instanceID, ok := instanceData["instance_id"]
		if !ok {
			continue
		}

		// 解析最后心跳时间
		var lastHeartbeat time.Time
		if hb, ok := instanceData["last_heartbeat"]; ok {
			lastHeartbeat, _ = time.Parse(time.RFC3339, hb)
		}

		// K8s环境下：过期实例直接跳过，不返回
		if time.Since(lastHeartbeat) > heartbeatTimeout {
			logger.Info(ctx, "发现过期实例，跳过显示: instanceID=%s, lastHeartbeat=%v", instanceID, lastHeartbeat)
			// 异步删除过期实例
			go func(id string) {
				tqm.cleanupExpiredInstance(context.Background(), id)
			}(instanceID)
			continue
		}

		maxTasks, _ := strconv.Atoi(instanceData["max_concurrent_tasks"])
		currentTasks, _ := tqm.getInstanceCurrentTasks(ctx, instanceID)

		// K8s环境下只有活跃状态
		instances = append(instances, types.InstanceInfo{
			InstanceID:         instanceID,
			MaxConcurrentTasks: maxTasks,
			CurrentTasks:       currentTasks,
			LastHeartbeat:      lastHeartbeat,
			Status:             "active", // K8s环境下只显示活跃实例
		})
	}

	return instances, nil
}
