# 任务生命周期管理改进

## 概述

本次改进主要解决了 Redis 任务队列中的内存泄漏问题，并增强了任务生命周期管理功能。

## 主要问题

1. **Redis 键内存泄漏**: 任务完成或取消后，Redis 中的任务键永远不会过期，导致内存持续增长
2. **取消键过期时间过短**: 原来只有 60 秒，在某些情况下可能不够
3. **缺少任务超时机制**: 没有自动清理长时间运行的任务

## 解决方案

### 1. 配置改进

在 `internal/config/config.go` 中添加了两个新的配置项：

```go
type TaskQueueConfig struct {
    // ... 其他配置 ...
    TaskRetentionTime int // 秒，任务完成后保留时间（24小时）
    CancelKeyTimeout  int // 秒，取消信号保留时间（30分钟）
}
```

**默认值:**
- `TaskRetentionTime`: 86400 秒 (24小时)
- `CancelKeyTimeout`: 1800 秒 (30分钟)

### 2. 任务键自动过期

修改了以下方法，确保所有任务键都会在 24 小时后自动过期：

- `SubmitTask`: 提交任务时设置过期时间
- `updateTaskStatus`: 更新任务状态时重新设置过期时间
- `CompleteTask`: 完成任务时设置过期时间
- 实例过期重新排队时也会重新设置过期时间

### 3. 取消键过期时间延长

- 将取消键的过期时间从 60 秒延长到 30 分钟
- 修改了 `sendCancelSignal` 方法和 Lua 脚本中的硬编码值

### 4. 任务超时清理机制

新增了 `CleanupTimeoutTasks` 方法：

```go
func (tqm *TaskQueueManager) CleanupTimeoutTasks(ctx context.Context) error
```

**功能:**
- 扫描所有正在处理的任务
- 检查任务是否超过配置的最大执行时间
- 自动将超时任务标记为失败
- 释放相关资源（部署锁、实例任务列表等）

### 5. 定期清理调度

在 `TaskScheduler` 中添加了定期清理循环：

```go
func (ts *TaskScheduler) timeoutCleanupLoop()
```

**特性:**
- 每 5 分钟执行一次超时清理
- 与其他调度器循环（心跳、任务消费）并行运行
- 在调度器停止时自动停止

## 技术细节

### Redis 键过期策略

1. **任务键** (`task:{taskID}`): 24小时后过期
2. **取消键** (`cancel:{taskID}`): 30分钟后过期
3. **实例键**: 保持原有的心跳机制
4. **锁键**: 在任务完成/失败/超时时立即删除

### 事务一致性

所有涉及多个 Redis 操作的地方都使用了 Pipeline 事务，确保原子性：

```go
pipe := tqm.redis.TxPipeline()
pipe.HMSet(ctx, taskKey, updates)
pipe.Expire(ctx, taskKey, time.Duration(tqm.config.TaskRetentionTime)*time.Second)
_, err := pipe.Exec(ctx)
```

### 超时检测逻辑

```go
// 检查任务是否超时
if time.Since(startedAt) > timeoutDuration {
    // 处理超时任务
}
```

## 测试覆盖

新增了专门的测试用例：

1. `TestTaskQueueManager_TaskTimeout`: 测试任务超时清理功能
2. 更新了现有测试以包含新的配置项
3. 修复了时间模拟相关的测试问题

## 环境变量配置

可以通过以下环境变量配置新功能：

```bash
# 任务保留时间（秒）
TASK_RETENTION_TIME=86400

# 取消键超时时间（秒）  
CANCEL_KEY_TIMEOUT=1800

# 任务最大执行时间（秒）
TASK_TIMEOUT=300
```

## 向后兼容性

- 所有现有功能保持不变
- 新配置项有合理的默认值
- 现有的 API 接口没有变化

## 性能影响

1. **内存使用**: 显著减少，因为任务键会自动过期
2. **Redis 性能**: 改善，因为键的数量会保持在合理范围内
3. **清理开销**: 最小，每 5 分钟只运行一次，且只扫描正在处理的任务

## 监控建议

建议监控以下指标：

1. Redis 内存使用量
2. 任务键的数量
3. 超时任务的数量
4. 清理操作的执行时间

## 总结

这次改进解决了任务队列系统中的关键内存泄漏问题，增强了系统的可靠性和可维护性。通过自动过期机制和超时清理，系统现在可以更好地处理长期运行的场景，避免资源泄漏。
