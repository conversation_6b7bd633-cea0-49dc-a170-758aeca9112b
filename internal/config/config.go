package config

import (
	"fmt"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	Server       ServerConfig
	Build        BuildConfig
	K8s          K8sConfig
	Registry     RegistryConfig
	Database     DatabaseConfig
	Notification NotificationConfig
	TaskQueue    TaskQueueConfig
}

type ServerConfig struct {
	Port string
	Host string
}

type BuildConfig struct {
	Host            string
	User            string
	KeyPath         string
	WorkDir         string
	WorkSpaceMgrURL string
}

type K8sConfig struct {
	ConfigPath      string
	Context         string
	Namespace       string
	ImagePullSecret string
}

type RegistryConfig struct {
	URL      string
	Username string
	Password string
}

type DatabaseConfig struct {
	URL             string
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime int // 连接最大生存时间（分钟）
}

type NotificationConfig struct {
	FeishuWebhookURL string
	BaseDomain       string
}

type TaskQueueConfig struct {
	// Redis 基础配置
	RedisAddr     string
	RedisPassword string
	RedisDB       int
	KeyPrefix     string

	// 任务配置
	MaxConcurrentTasks int
	TaskTimeout        int // 秒，单个任务最大执行时间
	QueueCheckInterval int // 秒
	LockExpireTime     int // 秒
	TaskRetentionTime  int // 秒，任务完成后保留时间（24小时）
	CancelKeyTimeout   int // 秒，取消信号保留时间（30分钟）

	// 实例和心跳配置
	HeartbeatInterval int // 秒
	InstanceTimeout   int // 秒，实例过期时间

	// Redis 连接配置
	PoolSize     int // 连接池大小
	MinIdleConns int // 最小空闲连接
	MaxIdleConns int // 最大空闲连接
	DialTimeout  int // 连接超时（秒）
	ReadTimeout  int // 读超时（秒）
	WriteTimeout int // 写超时（秒）
	PoolTimeout  int // 获取连接超时（秒）
	MaxRetries   int // 最大重试次数

	// 性能配置
	BatchSize           int // 批处理大小
	CancelCheckInterval int // 取消检查间隔（毫秒）

	// 监控配置
	EnableMetrics     bool // 是否启用指标
	MetricsInterval   int  // 指标收集间隔（秒）
	EnableHealthCheck bool // 是否启用健康检查
}

func Load() *Config {
	// 尝试加载 .env 文件（本地开发用）
	_ = godotenv.Load()

	return &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
		},
		Build: BuildConfig{
			Host:            getEnv("BUILD_HOST", ""),
			User:            getEnv("BUILD_USER", "root"),
			KeyPath:         getEnv("SSH_KEY_PATH", "/etc/ssh/id_rsa"),
			WorkDir:         getEnv("BUILD_WORK_DIR", "/tmp/builds"),
			WorkSpaceMgrURL: getEnv("WORKSPACE_MGR_URL", "http://wsmgr.mereith.com"),
		},
		K8s: K8sConfig{
			ConfigPath:      getEnv("KUBECONFIG", ""),
			Context:         getEnv("K8S_CONTEXT", ""),
			Namespace:       getEnv("K8S_NAMESPACE", "userspace"),
			ImagePullSecret: getEnv("K8S_IMAGE_PULL_SECRET", "acr-openwebui"),
		},
		Registry: RegistryConfig{
			URL:      getEnv("REGISTRY_URL", ""),
			Username: getEnv("REGISTRY_USERNAME", ""),
			Password: getEnv("REGISTRY_PASSWORD", ""),
		},
		Database: DatabaseConfig{
			URL:             getEnv("DATABASE_URL", ""),
			MaxOpenConns:    getEnvInt("DB_MAX_OPEN_CONNS", 25),
			MaxIdleConns:    getEnvInt("DB_MAX_IDLE_CONNS", 5),
			ConnMaxLifetime: getEnvInt("DB_CONN_MAX_LIFETIME", 5), // 5分钟
		},
		Notification: NotificationConfig{
			FeishuWebhookURL: getEnv("FEISHU_WEBHOOK_URL", ""),
			BaseDomain:       getEnv("BASE_DOMAIN", "space.chatglm.site"),
		},
		TaskQueue: TaskQueueConfig{
			// 基础配置
			RedisAddr:     getEnv("REDIS_ADDR", "localhost:6379"),
			RedisPassword: getEnv("REDIS_PASSWORD", ""),
			RedisDB:       getEnvInt("REDIS_DB", 1),
			KeyPrefix:     getEnv("REDIS_KEY_PREFIX", "task-queue:"),

			// 任务配置
			MaxConcurrentTasks: getEnvInt("MAX_CONCURRENT_TASKS", 10),
			TaskTimeout:        getEnvInt("TASK_TIMEOUT", 300),          // 5分钟
			QueueCheckInterval: getEnvInt("QUEUE_CHECK_INTERVAL", 5),    // 5秒
			LockExpireTime:     getEnvInt("LOCK_EXPIRE_TIME", 600),      // 10分钟
			TaskRetentionTime:  getEnvInt("TASK_RETENTION_TIME", 86400), // 24小时
			CancelKeyTimeout:   getEnvInt("CANCEL_KEY_TIMEOUT", 1800),   // 30分钟

			// 心跳配置
			HeartbeatInterval: getEnvInt("HEARTBEAT_INTERVAL", 30), // 30秒
			InstanceTimeout:   getEnvInt("INSTANCE_TIMEOUT", 90),   // 90秒（3倍心跳间隔）

			// Redis 连接配置
			PoolSize:     getEnvInt("REDIS_POOL_SIZE", 20),
			MinIdleConns: getEnvInt("REDIS_MIN_IDLE_CONNS", 5),
			MaxIdleConns: getEnvInt("REDIS_MAX_IDLE_CONNS", 10),
			DialTimeout:  getEnvInt("REDIS_DIAL_TIMEOUT", 5),  // 5秒
			ReadTimeout:  getEnvInt("REDIS_READ_TIMEOUT", 3),  // 3秒
			WriteTimeout: getEnvInt("REDIS_WRITE_TIMEOUT", 3), // 3秒
			PoolTimeout:  getEnvInt("REDIS_POOL_TIMEOUT", 4),  // 4秒
			MaxRetries:   getEnvInt("REDIS_MAX_RETRIES", 3),

			// 性能配置
			BatchSize:           getEnvInt("BATCH_SIZE", 100),
			CancelCheckInterval: getEnvInt("CANCEL_CHECK_INTERVAL", 500), // 500毫秒

			// 监控配置
			EnableMetrics:     getEnvBool("ENABLE_METRICS", true),
			MetricsInterval:   getEnvInt("METRICS_INTERVAL", 60), // 60秒
			EnableHealthCheck: getEnvBool("ENABLE_HEALTH_CHECK", true),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// Validate 验证任务队列配置
func (c *TaskQueueConfig) Validate() error {
	if c.RedisAddr == "" {
		return fmt.Errorf("Redis 地址不能为空")
	}

	if c.MaxConcurrentTasks <= 0 {
		return fmt.Errorf("最大并发任务数必须大于 0")
	}

	if c.HeartbeatInterval <= 0 {
		return fmt.Errorf("心跳间隔必须大于 0")
	}

	if c.InstanceTimeout <= c.HeartbeatInterval {
		return fmt.Errorf("实例超时时间必须大于心跳间隔")
	}

	if c.TaskTimeout <= 0 {
		return fmt.Errorf("任务超时时间必须大于 0")
	}

	return nil
}
