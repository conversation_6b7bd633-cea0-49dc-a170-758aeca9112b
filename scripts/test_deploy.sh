#!/bin/bash

# 快速部署测试脚本
# 用法: ./test_deploy.sh [server_url]

SERVER_URL=${1:-"http://localhost:8080"}
DEPLOYMENT_ID="test-$(date +%s)"
CHAT_ID="chat-$(date +%s)"
TRACE_ID="test-trace-$(date +%s)"

echo "🚀 开始部署测试"
echo "服务器地址: $SERVER_URL"
echo "部署ID: $DEPLOYMENT_ID"
echo "聊天ID: $CHAT_ID"
echo "追踪ID: $TRACE_ID"
echo "----------------------------------------"

# 1. 发起部署请求
echo "📤 发起部署请求..."
DEPLOY_RESPONSE=$(curl -s -X POST "$SERVER_URL/api/v1/deploy" \
    -H "Content-Type: application/json" \
    -H "x-trace-id: $TRACE_ID" \
    -d "{
        \"deploymentId\": \"$DEPLOYMENT_ID\",
        \"chatId\": \"$CHAT_ID\"
    }")

echo "部署响应: $DEPLOY_RESPONSE"

# 检查部署请求是否成功
DEPLOY_SUCCESS=$(echo "$DEPLOY_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
if [ "$DEPLOY_SUCCESS" != "true" ]; then
    echo "❌ 部署请求失败"
    exit 1
fi

echo "✅ 部署请求成功，开始监控状态..."
echo ""

# 2. 监控部署状态
echo "🔍 监控部署状态 (按 Ctrl+C 停止)..."
MONITOR_TRACE_ID="monitor-$TRACE_ID"
COUNTER=0
MAX_ATTEMPTS=60  # 最多监控 5 分钟 (60 * 5秒)

while [ $COUNTER -lt $MAX_ATTEMPTS ]; do
    # 查询部署状态
    STATUS_RESPONSE=$(curl -s -X POST "$SERVER_URL/api/v1/status" \
        -H "Content-Type: application/json" \
        -H "x-trace-id: $MONITOR_TRACE_ID" \
        -d "{\"deploymentId\": \"$DEPLOYMENT_ID\"}")
    
    # 检查 curl 是否成功
    if [ $? -ne 0 ]; then
        echo "[$(date '+%H:%M:%S')] ❌ 连接服务器失败"
        sleep 5
        COUNTER=$((COUNTER + 1))
        continue
    fi
    
    # 解析响应
    SUCCESS=$(echo "$STATUS_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    STATUS=$(echo "$STATUS_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    MESSAGE=$(echo "$STATUS_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    UPDATED_AT=$(echo "$STATUS_RESPONSE" | grep -o '"updatedAt":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    TIMESTAMP=$(date '+%H:%M:%S')
    
    if [ "$SUCCESS" = "true" ]; then
        case "$STATUS" in
            "pending")
                echo "[$TIMESTAMP] 🔄 部署进行中... (更新: $UPDATED_AT)"
                ;;
            "success")
                echo "[$TIMESTAMP] ✅ 部署成功! (完成: $UPDATED_AT)"
                echo ""
                echo "🎉 测试完成 - 部署成功!"
                echo "部署ID: $DEPLOYMENT_ID"
                echo "追踪ID: $TRACE_ID"
                exit 0
                ;;
            "failed")
                echo "[$TIMESTAMP] ❌ 部署失败: $MESSAGE (失败: $UPDATED_AT)"
                echo ""
                echo "💥 测试完成 - 部署失败!"
                echo "部署ID: $DEPLOYMENT_ID"
                echo "追踪ID: $TRACE_ID"
                echo "错误信息: $MESSAGE"
                exit 1
                ;;
            *)
                echo "[$TIMESTAMP] ❓ 未知状态: $STATUS"
                ;;
        esac
    else
        echo "[$TIMESTAMP] ❌ 查询失败: $MESSAGE"
    fi
    
    # 等待 5 秒后再次查询
    sleep 5
    COUNTER=$((COUNTER + 1))
done

echo ""
echo "⏰ 监控超时 (5分钟)"
echo "部署ID: $DEPLOYMENT_ID"
echo "追踪ID: $TRACE_ID"
echo "请手动检查部署状态"
