apiVersion: apps/v1
kind: Deployment
metadata:
  name: code-deploy-service
  namespace: default
  labels:
    app: code-deploy-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: code-deploy-service
  template:
    metadata:
      labels:
        app: code-deploy-service
    spec:
      serviceAccountName: code-deploy-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: code-deploy-service
        image: your-registry/code-deploy-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SERVER_PORT
          value: "8080"
        - name: BUILD_HOST
          valueFrom:
            configMapKeyRef:
              name: code-deploy-config
              key: BUILD_HOST
        - name: BUILD_USER
          valueFrom:
            configMapKeyRef:
              name: code-deploy-config
              key: BUILD_USER
        - name: REGISTRY_URL
          valueFrom:
            configMapKeyRef:
              name: code-deploy-config
              key: REGISTRY_URL
        - name: REGISTRY_USERNAME
          valueFrom:
            secretKeyRef:
              name: code-deploy-secrets
              key: R<PERSON><PERSON>TRY_USERNAME
        - name: <PERSON><PERSON><PERSON><PERSON><PERSON>_PASSWORD
          valueFrom:
            secretKeyRef:
              name: code-deploy-secrets
              key: REGISTRY_PASSWORD
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          capabilities:
            drop:
            - ALL
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: ssh-key
          mountPath: /etc/ssh
          readOnly: true
        - name: kubeconfig
          mountPath: /etc/kubeconfig
          readOnly: true
      volumes:
      - name: ssh-key
        secret:
          secretName: ssh-key
          defaultMode: 0600
      - name: kubeconfig
        secret:
          secretName: kubeconfig

---
apiVersion: v1
kind: Service
metadata:
  name: code-deploy-service
  namespace: default
  labels:
    app: code-deploy-service
spec:
  selector:
    app: code-deploy-service
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: code-deploy-service
  namespace: default

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: code-deploy-service
rules:
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: code-deploy-service
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: code-deploy-service
subjects:
- kind: ServiceAccount
  name: code-deploy-service
  namespace: default