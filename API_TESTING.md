# API 测试指南

## 导入 OpenAPI 规范

项目根目录下的 `openapi.json` 文件包含了完整的 API 规范，可以导入到以下工具中进行测试：

### Postman
1. 打开 Postman
2. 点击 "Import" 按钮
3. 选择 "File" 选项卡
4. 上传 `openapi.json` 文件
5. 点击 "Import" 完成导入

### Insomnia
1. 打开 Insomnia
2. 点击 "Create" -> "Import From" -> "File"
3. 选择 `openapi.json` 文件
4. 点击 "Scan" 然后 "Import"

### Swagger UI
1. 访问 [Swagger Editor](https://editor.swagger.io/)
2. 将 `openapi.json` 的内容复制粘贴到编辑器中
3. 可以直接在线测试 API

## 手动测试示例

### 1. 健康检查

```bash
curl -X GET http://localhost:8080/api/v1/health \
  -H "x-trace-id: health-check-001"
```

**预期响应：**
```json
{
  "status": "healthy"
}
```

### 2. 部署应用（异步，带代码URL）

```bash
curl -X POST http://localhost:8080/api/v1/deploy \
  -H "Content-Type: application/json" \
  -H "x-trace-id: deploy-with-url-001" \
  -d '{
    "deploymentId": "my-app-001",
    "chatId": "chat-session-123",
    "codeTarUrl": "https://example.com/my-code.tar.gz"
  }'
```

**预期响应（立即返回）：**
```json
{
  "success": true,
  "message": "部署已开始，请通过状态接口查询进度",
  "deploymentId": "my-app-001"
}
```

### 3. 部署应用（异步，不带代码URL）

```bash
curl -X POST http://localhost:8080/api/v1/deploy \
  -H "Content-Type: application/json" \
  -H "x-trace-id: deploy-no-url-001" \
  -d '{
    "deploymentId": "my-app-002",
    "chatId": "chat-session-456"
  }'
```

### 4. 查询部署状态

```bash
curl -X POST http://localhost:8080/api/v1/status \
  -H "Content-Type: application/json" \
  -H "x-trace-id: status-check-001" \
  -d '{
    "deploymentId": "my-app-001"
  }'
```

**预期响应示例：**

**进行中：**
```json
{
  "success": true,
  "message": "查询成功",
  "deploymentId": "my-app-001",
  "status": "pending",
  "traceId": "deploy-with-url-001",
  "updatedAt": "2025-07-14 16:30:45"
}
```

**成功：**
```json
{
  "success": true,
  "message": "查询成功",
  "deploymentId": "my-app-001",
  "status": "success",
  "traceId": "deploy-with-url-001",
  "updatedAt": "2025-07-14 16:35:20"
}
```

**失败：**
```json
{
  "success": true,
  "message": "查询成功",
  "deploymentId": "my-app-001",
  "status": "failed",
  "traceId": "deploy-with-url-001",
  "updatedAt": "2025-07-14 16:32:10"
}
```

### 5. 删除部署

```bash
curl -X DELETE http://localhost:8080/api/v1/deploy \
  -H "Content-Type: application/json" \
  -H "x-trace-id: delete-001" \
  -d '{
    "deploymentId": "my-app-001"
  }'
```

## 响应格式

### 成功响应示例

**部署开始（异步）：**
```json
{
  "success": true,
  "message": "部署已开始，请通过状态接口查询进度",
  "deploymentId": "my-app-001"
}
```

**状态查询成功：**
```json
{
  "success": true,
  "message": "查询成功",
  "deploymentId": "my-app-001",
  "status": "success",
  "traceId": "deploy-with-url-001",
  "updatedAt": "2025-07-14 16:35:20"
}
```

**删除成功：**
```json
{
  "success": true,
  "message": "Deployment deleted successfully"
}
```

### 错误响应示例

**请求参数错误：**
```json
{
  "success": false,
  "message": "请求格式无效: Key: 'DeployRequest.DeploymentID' Error:Field validation for 'DeploymentID' failed on the 'required' tag"
}
```

**服务器错误：**
```json
{
  "success": false,
  "message": "获取代码失败: Get \"invalid-url\": unsupported protocol scheme \"\""
}
```

## trace-id 功能

### 自动生成
如果请求中没有提供 `x-trace-id` header，系统会自动生成一个随机ID：

```bash
curl -X GET http://localhost:8080/api/v1/health
# 响应 header 中会包含: X-Trace-Id: eb4f7035feec8359e2e25e1a44cc3a7b
```

### 手动指定
可以在请求中指定 trace-id 用于追踪：

```bash
curl -X GET http://localhost:8080/api/v1/health \
  -H "x-trace-id: my-custom-trace-id"
# 响应 header 中会包含: X-Trace-Id: my-custom-trace-id
```

### 日志追踪
所有相关的日志都会包含 trace-id，方便问题排查：

```
2025/07/14 16:13:10 [信息] [追踪ID:my-custom-trace-id] 收到HTTP请求: GET /api/v1/health
2025/07/14 16:13:10 [信息] [追踪ID:my-custom-trace-id] HTTP请求完成: GET /api/v1/health, 状态码: 200
```

## 环境配置

### 本地开发
1. 复制 `.env.example` 为 `.env`
2. 根据实际环境修改配置
3. 运行 `go run cmd/main.go`

### 端口配置
默认端口是 8080，可以通过环境变量修改：

```bash
SERVER_PORT=8081 go run cmd/main.go
```

## Redis 配置

### 环境变量
```bash
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_KEY_PREFIX=code-deploy:
```

### 状态存储
- 部署状态存储在 Redis 中，key 格式：`{prefix}deployment:{deploymentId}`
- 状态自动过期时间：10分钟
- 支持的状态：`pending`（进行中）、`success`（成功）、`failed`（失败）

### 无 Redis 模式
如果 Redis 连接失败，服务器会：
- 跳过 Redis 初始化，继续启动
- 部署接口仍然可用，但状态不会保存
- 状态查询接口会返回"Redis服务未配置"错误

## 异步部署流程

### 1. 部署请求
- 客户端发送部署请求
- 服务器立即返回"部署已开始"
- 后台异步执行实际部署

### 2. 状态跟踪
- 初始状态：`pending`（部署正在进行中）
- 成功状态：`success`（部署成功，包含镜像和服务信息）
- 失败状态：`failed`（部署失败，包含错误信息）

### 3. 状态查询
- 使用 `/api/v1/status` 接口查询当前状态
- 包含完整的 trace-id 用于问题追踪
- 状态会自动过期（10分钟）

## 注意事项

1. **异步部署**：部署接口会立即返回，实际部署在后台进行
2. **状态查询**：需要通过状态接口查询部署进度和结果
3. **代码下载功能**：目前 `codeTarUrl` 参数支持从 HTTP/HTTPS URL 下载 tar 包
4. **空 tar 包处理**：如果不提供 `codeTarUrl`，系统会使用空的 tar 包
5. **错误处理**：所有错误都会记录在日志中，并返回中文错误信息
6. **trace-id 传递**：trace-id 会在整个请求处理流程中传递，便于问题追踪
7. **Redis 依赖**：状态查询功能需要 Redis 支持，无 Redis 时仅支持部署功能
