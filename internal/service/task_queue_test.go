package service

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"code-deploy-service/internal/config"
	"code-deploy-service/internal/types"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 测试用的 Redis mock 服务
func setupTestRedis(t *testing.T) (*miniredis.Miniredis, *config.TaskQueueConfig) {
	mr, err := miniredis.Run()
	require.NoError(t, err)

	cfg := &config.TaskQueueConfig{
		RedisAddr:           mr.Addr(),
		RedisPassword:       "",
		RedisDB:             0,
		KeyPrefix:           "test:",
		MaxConcurrentTasks:  5,
		TaskTimeout:         60,
		QueueCheckInterval:  1,
		LockExpireTime:      300,
		TaskRetentionTime:   86400, // 24小时
		CancelKeyTimeout:    1800,  // 30分钟
		HeartbeatInterval:   10,
		InstanceTimeout:     30,
		PoolSize:            10,
		MinIdleConns:        2,
		MaxIdleConns:        5,
		DialTimeout:         5,
		ReadTimeout:         3,
		WriteTimeout:        3,
		PoolTimeout:         4,
		MaxRetries:          3,
		BatchSize:           100,
		CancelCheckInterval: 100,
		EnableMetrics:       true,
		MetricsInterval:     60,
		EnableHealthCheck:   true,
	}

	t.Cleanup(func() {
		mr.Close()
	})

	return mr, cfg
}

func setupTestRedisWithTimeout(t *testing.T, instanceTimeout int) (*miniredis.Miniredis, *config.TaskQueueConfig) {
	mr, cfg := setupTestRedis(t)
	cfg.InstanceTimeout = instanceTimeout
	cfg.HeartbeatInterval = instanceTimeout / 3 // 确保心跳间隔小于实例超时
	return mr, cfg
}

// 创建测试任务
func createTestTask(taskID, deploymentID string) *types.DeployTask {
	return &types.DeployTask{
		TaskID:       taskID,
		DeploymentID: deploymentID,
		Request: &types.DeployRequest{
			DeploymentID: deploymentID,
			ChatID:       "test-chat",
			CodeTarURL:   "http://example.com/code.tar.gz",
		},
		UserInfo: &types.UserInfo{
			ID:    "user123",
			Email: "<EMAIL>",
		},
		TraceID:   "trace123",
		Priority:  1,
		CreatedAt: time.Now(),
		Status:    types.TaskStatusPending,
		Message:   "测试任务",
		Progress:  0,
	}
}

func TestTaskQueueManager_TaskTimeout(t *testing.T) {
	_, cfg := setupTestRedis(t)
	cfg.TaskTimeout = 2 // 2秒超时

	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(t, err)
	defer tqm.Close()

	ctx := context.Background()
	instanceID := "timeout-test-instance"

	// 注册实例
	err = tqm.RegisterInstance(ctx, instanceID)
	require.NoError(t, err)

	t.Run("任务超时清理", func(t *testing.T) {
		// 提交任务
		task := createTestTask("timeout-task", "timeout-deployment")
		err := tqm.SubmitTask(ctx, task)
		require.NoError(t, err)

		// 获取任务开始处理
		fetchedTask, err := tqm.GetNextTask(ctx, instanceID)
		require.NoError(t, err)
		require.NotNil(t, fetchedTask)
		assert.Equal(t, types.TaskStatusProcessing, fetchedTask.Status)

		// 手动设置任务开始时间为过去的时间（超过超时时间）
		pastTime := time.Now().Add(-time.Duration(cfg.TaskTimeout+10) * time.Second)
		err = tqm.redis.HSet(ctx, tqm.getTaskKey("timeout-task"), "started_at", pastTime.Format(time.RFC3339)).Err()
		require.NoError(t, err)

		// 执行超时清理
		err = tqm.CleanupTimeoutTasks(ctx)
		assert.NoError(t, err)

		// 验证任务状态被更新为失败
		timeoutTask, err := tqm.GetTask(ctx, "timeout-task")
		assert.NoError(t, err)
		assert.Equal(t, types.TaskStatusFailed, timeoutTask.Status)
		assert.Contains(t, timeoutTask.Message, "任务执行超时")

		// 验证实例任务列表已清空
		taskCount, err := tqm.getInstanceCurrentTasks(ctx, instanceID)
		assert.NoError(t, err)
		assert.Equal(t, 0, taskCount)

		// 验证部署锁已释放
		_, err = tqm.redis.Get(ctx, tqm.getLockKey("timeout-deployment")).Result()
		assert.Equal(t, redis.Nil, err)
	})
}

func TestTaskQueueManager_NewTaskQueueManager(t *testing.T) {
	t.Run("成功创建", func(t *testing.T) {
		_, cfg := setupTestRedis(t)

		tqm, err := NewTaskQueueManager(cfg)
		require.NoError(t, err)
		require.NotNil(t, tqm)

		defer tqm.Close()

		// 验证 Redis 连接
		assert.NotNil(t, tqm.redis)
		assert.NotNil(t, tqm.atomicTaskFetchScript)
		assert.NotNil(t, tqm.atomicLockAcquireScript)
	})

	t.Run("配置验证失败", func(t *testing.T) {
		cfg := &config.TaskQueueConfig{
			RedisAddr: "", // 空地址
		}

		tqm, err := NewTaskQueueManager(cfg)
		assert.Error(t, err)
		assert.Nil(t, tqm)
		assert.Contains(t, err.Error(), "Redis 地址不能为空")
	})

	t.Run("Redis连接失败", func(t *testing.T) {
		cfg := &config.TaskQueueConfig{
			RedisAddr:           "localhost:9999", // 不存在的端口
			RedisPassword:       "",
			RedisDB:             0,
			KeyPrefix:           "test:",
			MaxConcurrentTasks:  5,
			TaskTimeout:         60,
			QueueCheckInterval:  1,
			LockExpireTime:      300,
			TaskRetentionTime:   86400, // 24小时
			CancelKeyTimeout:    1800,  // 30分钟
			HeartbeatInterval:   10,
			InstanceTimeout:     30,
			PoolSize:            10,
			MinIdleConns:        2,
			MaxIdleConns:        5,
			DialTimeout:         1, // 短超时
			ReadTimeout:         1,
			WriteTimeout:        1,
			PoolTimeout:         1,
			MaxRetries:          1,
			BatchSize:           100,
			CancelCheckInterval: 100,
			EnableMetrics:       true,
			MetricsInterval:     60,
			EnableHealthCheck:   true,
		}

		tqm, err := NewTaskQueueManager(cfg)
		assert.Error(t, err)
		assert.Nil(t, tqm)
		assert.Contains(t, err.Error(), "Redis 连接失败")
	})
}

func TestTaskQueueManager_SubmitTask(t *testing.T) {
	_, cfg := setupTestRedis(t)
	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(t, err)
	defer tqm.Close()

	ctx := context.Background()

	t.Run("提交单个任务", func(t *testing.T) {
		task := createTestTask("task1", "deployment1")

		err := tqm.SubmitTask(ctx, task)
		assert.NoError(t, err)

		// 验证任务是否在队列中
		queueLen, err := tqm.redis.LLen(ctx, tqm.getQueueKey()).Result()
		assert.NoError(t, err)
		assert.Equal(t, int64(1), queueLen)

		// 验证任务数据是否存储
		storedTask, err := tqm.GetTask(ctx, "task1")
		assert.NoError(t, err)
		assert.NotNil(t, storedTask)
		assert.Equal(t, task.TaskID, storedTask.TaskID)
		assert.Equal(t, task.DeploymentID, storedTask.DeploymentID)

		// 验证部署锁是否设置
		lockValue, err := tqm.redis.Get(ctx, tqm.getLockKey("deployment1")).Result()
		assert.NoError(t, err)
		assert.Equal(t, "task1", lockValue)
	})

	t.Run("冲突任务处理", func(t *testing.T) {
		// 提交第一个任务
		task1 := createTestTask("task2", "deployment2")
		err := tqm.SubmitTask(ctx, task1)
		require.NoError(t, err)

		// 提交冲突任务
		task2 := createTestTask("task3", "deployment2") // 相同的deploymentID
		err = tqm.SubmitTask(ctx, task2)
		assert.NoError(t, err)

		// 验证取消信号是否发送给第一个任务
		cancelKey := tqm.getCancelKey("task2")
		exists, err := tqm.redis.Exists(ctx, cancelKey).Result()
		assert.NoError(t, err)
		assert.Equal(t, int64(1), exists)

		// 验证新任务获得了锁
		lockValue, err := tqm.redis.Get(ctx, tqm.getLockKey("deployment2")).Result()
		assert.NoError(t, err)
		assert.Equal(t, "task3", lockValue)
	})
}

func TestTaskQueueManager_GetNextTask(t *testing.T) {
	_, cfg := setupTestRedis(t)
	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(t, err)
	defer tqm.Close()

	ctx := context.Background()
	instanceID := "test-instance"

	t.Run("获取正常任务", func(t *testing.T) {
		// 提交任务
		task := createTestTask("task4", "deployment4")
		err := tqm.SubmitTask(ctx, task)
		require.NoError(t, err)

		// 获取任务
		fetchedTask, err := tqm.GetNextTask(ctx, instanceID)
		assert.NoError(t, err)
		assert.NotNil(t, fetchedTask)
		assert.Equal(t, "task4", fetchedTask.TaskID)
		assert.Equal(t, types.TaskStatusProcessing, fetchedTask.Status)
		assert.Equal(t, instanceID, fetchedTask.InstanceID)

		// 验证实例任务列表
		taskCount, err := tqm.getInstanceCurrentTasks(ctx, instanceID)
		assert.NoError(t, err)
		assert.Equal(t, 1, taskCount)
	})

	t.Run("队列为空", func(t *testing.T) {
		// 清空队列
		tqm.redis.Del(ctx, tqm.getQueueKey())

		task, err := tqm.GetNextTask(ctx, instanceID)
		assert.NoError(t, err)
		assert.Nil(t, task)
	})

	t.Run("并发限制", func(t *testing.T) {
		// 清理之前的测试数据
		tqm.redis.Del(ctx, tqm.getInstanceKey(instanceID)+":tasks")

		// 提交多个任务
		for i := 0; i < 10; i++ {
			task := createTestTask(fmt.Sprintf("task%d", i+10), fmt.Sprintf("deployment%d", i+10))
			err := tqm.SubmitTask(ctx, task)
			require.NoError(t, err)
		}

		// 获取任务直到达到并发限制
		fetchedCount := 0
		for i := 0; i < cfg.MaxConcurrentTasks+2; i++ {
			task, err := tqm.GetNextTask(ctx, instanceID)
			assert.NoError(t, err)
			if task != nil {
				fetchedCount++
			} else {
				break
			}
		}

		// 验证不超过并发限制
		assert.LessOrEqual(t, fetchedCount, cfg.MaxConcurrentTasks)
	})

	t.Run("自动跳过已取消任务", func(t *testing.T) {
		// 清理
		tqm.redis.Del(ctx, tqm.getInstanceKey(instanceID)+":tasks")
		tqm.redis.Del(ctx, tqm.getQueueKey())

		// 提交一个正常任务
		task2 := createTestTask("task21", "deployment21")
		err := tqm.SubmitTask(ctx, task2)
		require.NoError(t, err)

		// 提交任务并立即取消
		task := createTestTask("task20", "deployment20")
		err = tqm.SubmitTask(ctx, task)
		require.NoError(t, err)

		// 发送取消信号
		err = tqm.CancelTask(ctx, "task20", "测试取消")
		require.NoError(t, err)

		// 获取任务，由于task20在队列顶部但被取消，应该跳过它，然后获取到task21
		// 因为队列是LIFO（LPush + RPop），最后提交的task20会先被获取到
		fetchedTask, err := tqm.GetNextTask(ctx, instanceID)
		assert.NoError(t, err)
		assert.NotNil(t, fetchedTask)
		// 由于Lua脚本会自动跳过被取消的任务，应该获取到下一个有效任务

		// 验证获取到的是有效任务（不是被取消的任务）
		assert.NotEqual(t, "task20", fetchedTask.TaskID) // 不应该是被取消的任务

		// 由于Lua脚本会在处理过程中自动更新取消任务的状态，
		// 我们验证task20确实存在取消信号
		exists, err := tqm.redis.Exists(ctx, tqm.getCancelKey("task20")).Result()
		assert.NoError(t, err)
		assert.Equal(t, int64(1), exists) // 取消信号应该存在
	})
}

func TestTaskQueueManager_CompleteTask(t *testing.T) {
	_, cfg := setupTestRedis(t)
	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(t, err)
	defer tqm.Close()

	ctx := context.Background()
	instanceID := "test-instance"

	t.Run("成功完成任务", func(t *testing.T) {
		// 提交并获取任务
		task := createTestTask("task30", "deployment30")
		err := tqm.SubmitTask(ctx, task)
		require.NoError(t, err)

		fetchedTask, err := tqm.GetNextTask(ctx, instanceID)
		require.NoError(t, err)
		require.NotNil(t, fetchedTask)

		// 完成任务
		err = tqm.CompleteTask(ctx, "task30", instanceID, true, "任务成功完成")
		assert.NoError(t, err)

		// 验证任务状态
		completedTask, err := tqm.GetTask(ctx, "task30")
		assert.NoError(t, err)
		assert.Equal(t, types.TaskStatusCompleted, completedTask.Status)

		// 验证实例任务列表已清空
		taskCount, err := tqm.getInstanceCurrentTasks(ctx, instanceID)
		assert.NoError(t, err)
		assert.Equal(t, 0, taskCount)

		// 验证部署锁已释放
		_, err = tqm.redis.Get(ctx, tqm.getLockKey("deployment30")).Result()
		assert.Equal(t, redis.Nil, err)
	})

	t.Run("失败完成任务", func(t *testing.T) {
		// 提交并获取任务
		task := createTestTask("task31", "deployment31")
		err := tqm.SubmitTask(ctx, task)
		require.NoError(t, err)

		fetchedTask, err := tqm.GetNextTask(ctx, instanceID)
		require.NoError(t, err)
		require.NotNil(t, fetchedTask)

		// 失败完成任务
		err = tqm.CompleteTask(ctx, "task31", instanceID, false, "任务执行失败")
		assert.NoError(t, err)

		// 验证任务状态
		failedTask, err := tqm.GetTask(ctx, "task31")
		assert.NoError(t, err)
		assert.Equal(t, types.TaskStatusFailed, failedTask.Status)
		assert.Equal(t, "任务执行失败", failedTask.Message)
	})
}

func TestTaskQueueManager_ConcurrentOperations(t *testing.T) {
	_, cfg := setupTestRedis(t)
	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(t, err)
	defer tqm.Close()

	ctx := context.Background()

	t.Run("并发提交任务", func(t *testing.T) {
		const numTasks = 50
		var wg sync.WaitGroup
		errors := make(chan error, numTasks)

		// 并发提交任务
		for i := 0; i < numTasks; i++ {
			wg.Add(1)
			go func(taskNum int) {
				defer wg.Done()
				task := createTestTask(fmt.Sprintf("concurrent-task-%d", taskNum), fmt.Sprintf("concurrent-deploy-%d", taskNum))
				if err := tqm.SubmitTask(ctx, task); err != nil {
					errors <- err
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// 检查是否有错误
		for err := range errors {
			t.Errorf("并发提交任务失败: %v", err)
		}

		// 验证队列中的任务数
		queueLen, err := tqm.redis.LLen(ctx, tqm.getQueueKey()).Result()
		assert.NoError(t, err)
		assert.Equal(t, int64(numTasks), queueLen)
	})

	t.Run("并发获取任务", func(t *testing.T) {
		const numInstances = 5
		var wg sync.WaitGroup
		fetchedTasks := make(chan string, 100)

		// 并发获取任务
		for i := 0; i < numInstances; i++ {
			wg.Add(1)
			go func(instanceNum int) {
				defer wg.Done()
				instanceID := fmt.Sprintf("instance-%d", instanceNum)

				for j := 0; j < 10; j++ {
					task, err := tqm.GetNextTask(ctx, instanceID)
					if err != nil {
						t.Errorf("获取任务失败: %v", err)
						return
					}
					if task != nil {
						fetchedTasks <- task.TaskID
					}
				}
			}(i)
		}

		wg.Wait()
		close(fetchedTasks)

		// 统计获取到的任务
		taskMap := make(map[string]bool)
		for taskID := range fetchedTasks {
			if taskMap[taskID] {
				t.Errorf("任务重复获取: %s", taskID)
			}
			taskMap[taskID] = true
		}

		t.Logf("成功获取 %d 个唯一任务", len(taskMap))
	})

	t.Run("冲突任务并发处理", func(t *testing.T) {
		const numConflictTasks = 10
		deploymentID := "conflict-deployment"
		var wg sync.WaitGroup

		// 并发提交相同 deploymentID 的任务
		for i := 0; i < numConflictTasks; i++ {
			wg.Add(1)
			go func(taskNum int) {
				defer wg.Done()
				task := createTestTask(fmt.Sprintf("conflict-task-%d", taskNum), deploymentID)
				tqm.SubmitTask(ctx, task)
			}(i)
		}

		wg.Wait()

		// 验证只有一个任务持有锁
		lockValue, err := tqm.redis.Get(ctx, tqm.getLockKey(deploymentID)).Result()
		assert.NoError(t, err)
		assert.NotEmpty(t, lockValue)
		t.Logf("最终获得锁的任务: %s", lockValue)

		// 验证其他任务收到了取消信号
		cancelCount := 0
		for i := 0; i < numConflictTasks; i++ {
			taskID := fmt.Sprintf("conflict-task-%d", i)
			if taskID != lockValue {
				exists, err := tqm.redis.Exists(ctx, tqm.getCancelKey(taskID)).Result()
				assert.NoError(t, err)
				if exists > 0 {
					cancelCount++
				}
			}
		}

		t.Logf("收到取消信号的任务数: %d", cancelCount)
		assert.Greater(t, cancelCount, 0)
	})
}

func TestTaskQueueManager_InstanceManagement(t *testing.T) {
	_, cfg := setupTestRedis(t)
	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(t, err)
	defer tqm.Close()

	ctx := context.Background()
	instanceID := "test-instance-mgmt"

	t.Run("注册实例", func(t *testing.T) {
		err := tqm.RegisterInstance(ctx, instanceID)
		assert.NoError(t, err)

		// 验证实例数据
		instanceData, err := tqm.redis.HGetAll(ctx, tqm.getInstanceKey(instanceID)).Result()
		assert.NoError(t, err)
		assert.Equal(t, instanceID, instanceData["instance_id"])
		assert.NotEmpty(t, instanceData["registered_at"])
		assert.NotEmpty(t, instanceData["last_heartbeat"])
	})

	t.Run("更新心跳", func(t *testing.T) {
		// 获取当前心跳时间
		oldHeartbeat, err := tqm.redis.HGet(ctx, tqm.getInstanceKey(instanceID), "last_heartbeat").Result()
		require.NoError(t, err)

		time.Sleep(1 * time.Second) // 确保时间差足够明显

		// 更新心跳
		err = tqm.UpdateHeartbeat(ctx, instanceID)
		assert.NoError(t, err)

		// 验证心跳时间已更新
		newHeartbeat, err := tqm.redis.HGet(ctx, tqm.getInstanceKey(instanceID), "last_heartbeat").Result()
		assert.NoError(t, err)
		assert.NotEqual(t, oldHeartbeat, newHeartbeat)
	})

	t.Run("注销实例", func(t *testing.T) {
		err := tqm.UnregisterInstance(ctx, instanceID)
		assert.NoError(t, err)

		// 验证实例数据已删除
		exists, err := tqm.redis.Exists(ctx, tqm.getInstanceKey(instanceID)).Result()
		assert.NoError(t, err)
		assert.Equal(t, int64(0), exists)
	})
}

func TestTaskQueueManager_ExpiredInstanceCleanup(t *testing.T) {
	_, cfg := setupTestRedisWithTimeout(t, 3) // 3秒超时，1秒心跳

	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(t, err)
	defer tqm.Close()

	ctx := context.Background()
	expiredInstanceID := "expired-instance"

	t.Run("清理过期实例", func(t *testing.T) {
		// 注册实例
		err := tqm.RegisterInstance(ctx, expiredInstanceID)
		require.NoError(t, err)

		// 给实例分配任务
		task := createTestTask("expired-task", "expired-deployment")
		err = tqm.SubmitTask(ctx, task)
		require.NoError(t, err)

		fetchedTask, err := tqm.GetNextTask(ctx, expiredInstanceID)
		require.NoError(t, err)
		require.NotNil(t, fetchedTask)

		// 手动设置实例心跳时间为过去的时间（超过超时时间）
		pastTime := time.Now().Add(-time.Duration(cfg.InstanceTimeout+10) * time.Second)
		err = tqm.redis.HSet(ctx, tqm.getInstanceKey(expiredInstanceID), "last_heartbeat", pastTime.Format(time.RFC3339)).Err()
		require.NoError(t, err)

		// 执行清理
		err = tqm.CleanupExpiredInstances(ctx)
		assert.NoError(t, err)

		// 验证实例已被清理
		exists, err := tqm.redis.Exists(ctx, tqm.getInstanceKey(expiredInstanceID)).Result()
		assert.NoError(t, err)
		assert.Equal(t, int64(0), exists)

		// 验证任务被重新排队
		queueLen, err := tqm.redis.LLen(ctx, tqm.getQueueKey()).Result()
		assert.NoError(t, err)
		assert.Greater(t, queueLen, int64(0))

		// 验证任务状态被重置
		requeuedTask, err := tqm.GetTask(ctx, "expired-task")
		assert.NoError(t, err)
		assert.Equal(t, types.TaskStatusPending, requeuedTask.Status)
		assert.Empty(t, requeuedTask.InstanceID)
	})
}

// 性能测试
func BenchmarkTaskQueueManager_SubmitTask(b *testing.B) {
	_, cfg := setupTestRedis(&testing.T{})
	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(b, err)
	defer tqm.Close()

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			task := createTestTask(fmt.Sprintf("bench-task-%d", i), fmt.Sprintf("bench-deploy-%d", i))
			tqm.SubmitTask(ctx, task)
			i++
		}
	})
}

func BenchmarkTaskQueueManager_GetNextTask(b *testing.B) {
	_, cfg := setupTestRedis(&testing.T{})
	tqm, err := NewTaskQueueManager(cfg)
	require.NoError(b, err)
	defer tqm.Close()

	ctx := context.Background()

	// 预填充任务
	for i := 0; i < 1000; i++ {
		task := createTestTask(fmt.Sprintf("bench-get-task-%d", i), fmt.Sprintf("bench-get-deploy-%d", i))
		tqm.SubmitTask(ctx, task)
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			tqm.GetNextTask(ctx, fmt.Sprintf("bench-instance-%d", i%10))
			i++
		}
	})
}
