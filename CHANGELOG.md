# 更新日志

## 2025-07-14 - 添加异步部署和 Redis 状态管理

### 新增功能

#### 1. 异步部署
- 部署接口现在立即返回，不再等待构建完成
- 实际部署过程在后台异步执行
- 大幅提升用户体验，避免长时间等待

#### 2. Redis 状态管理
- 集成 Redis 用于存储部署状态
- 支持可配置的 key 前缀
- 状态自动过期（10分钟）
- 优雅处理 Redis 连接失败

#### 3. 部署状态查询
- 新增 `/api/v1/status` 接口查询部署状态
- 支持三种状态：`pending`（进行中）、`success`（成功）、`failed`（失败）
- 包含详细的错误信息和 trace-id

#### 4. 增强的错误处理
- 完整的中文错误信息
- trace-id 在异步流程中正确传递
- Redis 连接失败时的优雅降级

### 技术改进

#### 异步架构
- 使用 goroutine 实现异步部署
- 独立的 context 管理，不依赖原始请求
- 完整的错误处理和状态更新

#### Redis 集成
- 使用 `github.com/redis/go-redis/v9` 客户端
- JSON 序列化存储复杂状态信息
- 自动过期和清理机制

### API 更新

#### 部署接口变更
**之前（同步）：**
```json
{
  "success": true,
  "message": "Deployment successful",
  "deploymentId": "my-app-001",
  "imageUrl": "registry.example.com/my-app:latest",
  "serviceUrl": "my-app.default.svc.cluster.local:3000"
}
```

**现在（异步）：**
```json
{
  "success": true,
  "message": "部署已开始，请通过状态接口查询进度",
  "deploymentId": "my-app-001"
}
```

#### 新增状态查询接口
```bash
POST /api/v1/status
{
  "deploymentId": "my-app-001"
}
```

### 配置更新

#### Redis 配置
```bash
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=your-password
REDIS_DB=0
REDIS_KEY_PREFIX=code-deploy:
```

### 测试验证

#### 功能测试
✅ 异步部署立即返回
✅ Redis 状态正确存储和查询
✅ 无 Redis 时优雅降级
✅ trace-id 在异步流程中正确传递
✅ 错误状态正确记录和返回

#### 性能提升
- 部署接口响应时间：从分钟级降低到毫秒级
- 用户体验：无需长时间等待，可通过状态接口查询进度
- 系统稳定性：Redis 连接失败不影响核心部署功能

---

## 2025-07-14 - 添加代码下载功能和 OpenAPI 规范

### 新增功能

#### 1. codeTarUrl 参数支持
- 在 `DeployRequest` 中添加了可选的 `codeTarUrl` 参数
- 支持从指定 URL 下载代码 tar 包
- 如果未提供 URL，则使用空的 tar 包（预留给后续扩展）
- 包含完整的错误处理和中文日志记录

#### 2. OpenAPI 规范文档
- 创建了完整的 `openapi.json` 文件
- 包含所有 API 端点的详细规范
- 支持导入到 Postman、Insomnia 等工具进行测试
- 包含中文描述和示例数据

### API 更新

#### 部署接口增强
```json
{
  "deploymentId": "deploy-123456",
  "chatId": "chat-789abc",
  "codeTarUrl": "https://example.com/code.tar.gz"  // 新增可选参数
}
```

### 测试验证

#### 功能测试
✅ codeTarUrl 参数正确接收和处理
✅ 代码下载功能正常工作
✅ 无 codeTarUrl 时返回空 tar 包
✅ 错误处理和日志记录完整
✅ trace-id 在整个流程中正确传递

#### 示例日志输出
```
2025/07/14 16:13:10 [信息] [追踪ID:test-deploy-123] 开始下载代码包: https://httpbin.org/status/200
2025/07/14 16:13:11 [信息] [追踪ID:test-deploy-123] 代码包下载成功，大小: 0 字节
2025/07/14 16:15:06 [信息] [追踪ID:test-deploy-456] 未提供代码URL，返回空的tar包
```

---

## 2025-07-14 - 添加本地开发支持和中文日志

### 新增功能

#### 1. dotenv 支持
- 添加了 `github.com/joho/godotenv` 依赖
- 在配置加载时自动尝试加载 `.env` 文件
- 创建了 `.env` 示例文件，方便本地开发配置

#### 2. 中文日志系统
- 创建了统一的日志工具包 `pkg/logger`
- 支持中文日志输出，提升中文用户体验
- 包含以下日志级别：
  - `Info` - 信息级别
  - `Error` - 错误级别  
  - `Warn` - 警告级别
  - `Debug` - 调试级别
  - `Fatal` - 致命错误级别
- 提供简化版本（不需要 context）用于启动阶段

#### 3. trace-id 中间件
- 实现了 HTTP 请求追踪功能
- 从 `x-trace-id` header 读取追踪ID，如果没有则自动生成随机ID
- 将 trace-id 注入到 context 中，供后续日志使用
- 在响应 header 中返回 trace-id，方便调试
- 所有日志都包含 trace-id，格式：`[追踪ID:xxx]`

### 技术改进

#### 代码重构
- 将所有 `log.Printf` 调用替换为新的中文日志工具
- 更新函数签名，添加 `context.Context` 参数以支持 trace-id
- 移除了对标准 `log` 包的依赖

#### 日志格式优化
- 统一的时间戳格式：`[2025-07-14 15:20:32]`
- 中文日志级别标识：`[信息]`、`[错误]`、`[警告]` 等
- trace-id 集成：`[追踪ID:eb4f7035feec8359e2e25e1a44cc3a7b]`

### 测试验证

#### 功能测试
✅ dotenv 配置加载正常
✅ 中文日志输出正确
✅ trace-id 从 header 正确读取
✅ trace-id 自动生成功能正常
✅ trace-id 在日志中正确显示
✅ 服务器启动和HTTP请求处理正常

#### 示例日志输出
```
2025/07/14 15:20:32 [2025-07-14 15:20:32] [信息] [追踪ID:test-123] 收到HTTP请求: GET /api/v1/health
2025/07/14 15:20:32 [2025-07-14 15:20:32] [信息] [追踪ID:test-123] HTTP请求完成: GET /api/v1/health, 状态码: 200
```

### 使用说明

#### 本地开发
1. 复制 `.env` 文件并配置相应的环境变量
2. 运行 `go run cmd/main.go` 启动服务

#### trace-id 使用
- 客户端可以在请求中添加 `x-trace-id` header
- 如果不提供，系统会自动生成一个随机ID
- 响应中会包含 `X-Trace-Id` header
- 所有相关日志都会包含该 trace-id，方便问题排查

### 文件变更

#### 新增文件
- `internal/service/redis.go` - Redis 状态管理服务
- `openapi.json` - OpenAPI 3.0 规范文档（已更新状态查询接口）
- `pkg/logger/logger.go` - 中文日志工具包
- `internal/middleware/trace.go` - trace-id 中间件
- `.env` - 环境变量示例文件（已添加 Redis 配置）
- `CHANGELOG.md` - 更新日志
- `API_TESTING.md` - API 测试指南（已更新异步部署说明）

#### 修改文件
- `internal/service/deploy.go` - 重构为异步部署，添加 Redis 状态管理
- `internal/types/types.go` - 添加状态查询相关类型定义
- `internal/api/handler.go` - 添加状态查询接口
- `internal/api/routes.go` - 添加状态查询路由
- `internal/config/config.go` - 添加 Redis 配置
- `cmd/main.go` - 添加 Redis 服务初始化
- `go.mod` - 添加 redis 客户端依赖
