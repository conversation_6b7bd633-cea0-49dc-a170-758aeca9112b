package middleware

import (
	"code-deploy-service/pkg/logger"

	"github.com/gin-gonic/gin"
)

// TraceIDMiddleware 中间件，从 header 读取或生成 trace-id
func TraceIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从 header 中读取 x-trace-id
		traceID := c.GetHeader("x-trace-id")
		
		// 如果没有 trace-id，则生成一个随机的
		if traceID == "" {
			traceID = generateTraceID()
		}
		
		// 将 trace-id 注入到 context 中
		ctx := logger.WithTraceID(c.Request.Context(), traceID)
		c.Request = c.Request.WithContext(ctx)
		
		// 将 trace-id 添加到响应 header 中，方便调试
		c.Header("x-trace-id", traceID)
		
		// 记录请求开始日志
		logger.Info(ctx, "收到HTTP请求: %s %s", c.Request.Method, c.Request.URL.Path)
		
		// 继续处理请求
		c.Next()
		
		// 记录请求完成日志
		logger.Info(ctx, "HTTP请求完成: %s %s, 状态码: %d", c.Request.Method, c.Request.URL.Path, c.Writer.Status())
	}
}

// 生成随机 trace ID（与 logger 包中的函数相同）
func generateTraceID() string {
	return logger.GenerateTraceID()
}
