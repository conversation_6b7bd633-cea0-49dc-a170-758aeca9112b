{"openapi": "3.0.3", "info": {"title": "Code Deploy Service API", "description": "一个运行在 Kubernetes 环境中的代码部署服务，提供 HTTP 接口来部署和管理应用程序。", "version": "1.0.0", "contact": {"name": "Code Deploy Service", "url": "https://github.com/your-org/code-deploy-service"}}, "servers": [{"url": "http://localhost:8080", "description": "本地开发服务器"}], "paths": {"/api/v1/deploy": {"post": {"summary": "部署应用", "description": "部署一个新的应用到 Kubernetes 集群", "operationId": "deployApp", "tags": ["部署管理"], "parameters": [{"name": "x-trace-id", "in": "header", "description": "请求追踪ID，如果不提供将自动生成", "required": false, "schema": {"type": "string", "example": "trace-123456"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeployRequest"}}}}, "responses": {"200": {"description": "部署成功", "headers": {"x-trace-id": {"description": "请求追踪ID", "schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeployResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"summary": "删除部署", "description": "删除指定的应用部署", "operationId": "deleteApp", "tags": ["部署管理"], "parameters": [{"name": "x-trace-id", "in": "header", "description": "请求追踪ID，如果不提供将自动生成", "required": false, "schema": {"type": "string", "example": "trace-123456"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRequest"}}}}, "responses": {"200": {"description": "删除成功", "headers": {"x-trace-id": {"description": "请求追踪ID", "schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/status": {"post": {"summary": "查询部署状态", "description": "查询指定部署的当前状态", "operationId": "getDeploymentStatus", "tags": ["部署管理"], "parameters": [{"name": "x-trace-id", "in": "header", "description": "请求追踪ID，如果不提供将自动生成", "required": false, "schema": {"type": "string", "example": "trace-123456"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusRequest"}}}}, "responses": {"200": {"description": "查询成功", "headers": {"x-trace-id": {"description": "请求追踪ID", "schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusResponse"}}}}, "404": {"description": "部署状态不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/health": {"get": {"summary": "健康检查", "description": "检查服务健康状态", "operationId": "healthCheck", "tags": ["系统"], "parameters": [{"name": "x-trace-id", "in": "header", "description": "请求追踪ID，如果不提供将自动生成", "required": false, "schema": {"type": "string", "example": "trace-123456"}}], "responses": {"200": {"description": "服务健康", "headers": {"x-trace-id": {"description": "请求追踪ID", "schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "healthy"}}}}}}}}}}, "components": {"schemas": {"DeployRequest": {"type": "object", "required": ["deploymentId", "chatId"], "properties": {"deploymentId": {"type": "string", "description": "部署ID，用于标识此次部署", "example": "deploy-123456"}, "chatId": {"type": "string", "description": "聊天会话ID", "example": "chat-789abc"}, "codeTarUrl": {"type": "string", "description": "代码tar包的下载URL（可选）", "example": "https://example.com/code.tar.gz"}}}, "DeployResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "部署是否成功"}, "message": {"type": "string", "description": "响应消息"}, "deploymentId": {"type": "string", "description": "部署ID"}, "imageUrl": {"type": "string", "description": "构建的镜像URL"}, "serviceUrl": {"type": "string", "description": "服务访问URL"}}}, "DeleteRequest": {"type": "object", "required": ["deploymentId"], "properties": {"deploymentId": {"type": "string", "description": "要删除的部署ID", "example": "deploy-123456"}}}, "DeleteResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "删除是否成功"}, "message": {"type": "string", "description": "响应消息"}}}, "StatusRequest": {"type": "object", "required": ["deploymentId"], "properties": {"deploymentId": {"type": "string", "description": "要查询的部署ID", "example": "deploy-123456"}}}, "StatusResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "查询是否成功"}, "message": {"type": "string", "description": "响应消息"}, "deploymentId": {"type": "string", "description": "部署ID"}, "status": {"type": "string", "description": "部署状态", "enum": ["pending", "success", "failed"], "example": "success"}, "traceId": {"type": "string", "description": "追踪ID", "example": "trace-123456"}, "updatedAt": {"type": "string", "description": "状态更新时间", "example": "2025-07-14 16:30:45"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "description": "错误消息"}}}}}, "tags": [{"name": "部署管理", "description": "应用部署相关操作"}, {"name": "系统", "description": "系统状态和健康检查"}]}