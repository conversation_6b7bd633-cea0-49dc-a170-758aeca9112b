package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"code-deploy-service/internal/config"
	"code-deploy-service/internal/types"
	"code-deploy-service/pkg/logger"

	"github.com/google/uuid"
)

type TaskScheduler struct {
	queueManager     *TaskQueueManager
	deployService    *DeployService
	config           *config.TaskQueueConfig
	instanceID       string
	ctx              context.Context
	cancel           context.CancelFunc
	wg               sync.WaitGroup
	isRunning        bool
	mu               sync.RWMutex
	activeTasksCount int
	activeTasks      map[string]context.CancelFunc // taskID -> cancel function
}

func NewTaskScheduler(queueManager *TaskQueueManager, deployService *DeployService, cfg *config.TaskQueueConfig) *TaskScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	// 生成实例ID
	instanceID := generateInstanceID()

	return &TaskScheduler{
		queueManager:  queueManager,
		deployService: deployService,
		config:        cfg,
		instanceID:    instanceID,
		ctx:           ctx,
		cancel:        cancel,
		isRunning:     false,
		activeTasks:   make(map[string]context.CancelFunc),
	}
}

func generateInstanceID() string {
	// 使用 UUID + 时间戳生成唯一实例ID
	return fmt.Sprintf("instance-%s-%d", uuid.New().String()[:8], time.Now().Unix())
}

func (ts *TaskScheduler) GetInstanceID() string {
	return ts.instanceID
}

func (ts *TaskScheduler) Start() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.isRunning {
		return fmt.Errorf("任务调度器已经在运行")
	}

	logger.Info(ts.ctx, "启动任务调度器: instanceID=%s", ts.instanceID)

	// 注册实例
	err := ts.queueManager.RegisterInstance(ts.ctx, ts.instanceID)
	if err != nil {
		return fmt.Errorf("注册实例失败: %v", err)
	}

	ts.isRunning = true

	// 启动心跳
	ts.wg.Add(1)
	go ts.heartbeatLoop()

	// 启动任务消费
	ts.wg.Add(1)
	go ts.taskConsumerLoop()

	// 启动超时清理（仅在主实例上运行）
	ts.wg.Add(1)
	go ts.timeoutCleanupLoop()

	logger.Info(ts.ctx, "任务调度器启动成功: instanceID=%s", ts.instanceID)
	return nil
}

func (ts *TaskScheduler) Stop() {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if !ts.isRunning {
		return
	}

	logger.Info(ts.ctx, "停止K8s任务调度器: instanceID=%s", ts.instanceID)

	ts.isRunning = false
	ts.cancel()

	// 取消所有活跃任务
	for taskID, cancelFunc := range ts.activeTasks {
		logger.Info(ts.ctx, "取消活跃任务: taskID=%s", taskID)
		cancelFunc()
	}

	// 等待所有 goroutine 结束
	ts.wg.Wait()

	// 直接删除实例（K8s环境下Pod重启实例ID会变）
	ctx := context.Background() // 使用新的上下文，因为原上下文已被取消
	err := ts.queueManager.UnregisterInstance(ctx, ts.instanceID)
	if err != nil {
		logger.Error(ctx, "删除K8s实例失败: instanceID=%s, error=%v", ts.instanceID, err)
	}

	logger.Info(ts.ctx, "K8s任务调度器已停止并删除实例: instanceID=%s", ts.instanceID)
}

func (ts *TaskScheduler) IsRunning() bool {
	ts.mu.RLock()
	defer ts.mu.RUnlock()
	return ts.isRunning
}

func (ts *TaskScheduler) GetActiveTasksCount() int {
	ts.mu.RLock()
	defer ts.mu.RUnlock()
	return ts.activeTasksCount
}

// heartbeatLoop 心跳循环
func (ts *TaskScheduler) heartbeatLoop() {
	defer ts.wg.Done()

	ticker := time.NewTicker(time.Duration(ts.config.HeartbeatInterval) * time.Second)
	defer ticker.Stop()

	// 清理过期实例的计数器 (每10次心跳清理一次)
	cleanupCounter := 0

	for {
		select {
		case <-ts.ctx.Done():
			return
		case <-ticker.C:
			// 更新心跳
			err := ts.queueManager.UpdateHeartbeat(context.Background(), ts.instanceID)
			if err != nil {
				logger.Error(ts.ctx, "更新心跳失败: %v", err)
			}

			// 定期清理过期实例
			cleanupCounter++
			if cleanupCounter >= 10 { // 每10次心跳清理一次
				cleanupCounter = 0
				err := ts.queueManager.CleanupExpiredInstances(context.Background())
				if err != nil {
					logger.Error(ts.ctx, "清理过期实例失败: %v", err)
				}
			}
		}
	}
}

// taskConsumerLoop 任务消费循环
func (ts *TaskScheduler) taskConsumerLoop() {
	defer ts.wg.Done()

	checkInterval := time.Duration(ts.config.QueueCheckInterval) * time.Second
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ts.ctx.Done():
			return
		case <-ticker.C:
			ts.processNextTask()
		}
	}
}

// processNextTask 处理下一个任务
func (ts *TaskScheduler) processNextTask() {
	// 检查是否还能接受新任务
	ts.mu.RLock()
	if !ts.isRunning || ts.activeTasksCount >= ts.config.MaxConcurrentTasks {
		ts.mu.RUnlock()
		return
	}
	ts.mu.RUnlock()

	// 获取下一个任务
	task, err := ts.queueManager.GetNextTask(context.Background(), ts.instanceID)
	if err != nil {
		logger.Error(ts.ctx, "获取任务失败: %v", err)
		return
	}

	if task == nil {
		// 没有任务，继续等待
		return
	}

	// 执行任务
	ts.executeTask(task)
}

// executeTask 执行任务
func (ts *TaskScheduler) executeTask(task *types.DeployTask) {
	ts.mu.Lock()
	ts.activeTasksCount++
	ts.mu.Unlock()

	// 创建任务专用的 context 用于取消
	taskCtx, taskCancel := context.WithCancel(context.Background())
	taskCtx = logger.WithTraceID(taskCtx, task.TraceID)

	ts.mu.Lock()
	ts.activeTasks[task.TaskID] = taskCancel
	ts.mu.Unlock()

	logger.Info(taskCtx, "开始执行任务: taskID=%s, deploymentID=%s", task.TaskID, task.DeploymentID)

	// 在新的 goroutine 中执行任务
	go func() {
		defer func() {
			ts.mu.Lock()
			delete(ts.activeTasks, task.TaskID)
			ts.activeTasksCount--
			ts.mu.Unlock()
		}()

		success := false
		message := ""

		// 创建任务取消监听器
		go ts.taskCancelListener(taskCtx, taskCancel, task.TaskID)

		// 执行部署任务
		err := ts.executeDeployTask(taskCtx, task)
		if err != nil {
			// 检查是否是取消操作
			if taskCtx.Err() == context.Canceled {
				message = "任务被取消"
				logger.Info(taskCtx, "任务被取消: taskID=%s", task.TaskID)

				// 更新任务状态为取消
				ts.queueManager.UpdateTaskStatus(context.Background(), task.TaskID, types.TaskStatusCancelled, message)
			} else {
				success = false
				message = fmt.Sprintf("部署失败: %v", err)
				logger.Error(taskCtx, "任务执行失败: taskID=%s, error=%v", task.TaskID, err)
			}
		} else {
			success = true
			message = "部署成功"
			logger.Info(taskCtx, "任务执行成功: taskID=%s", task.TaskID)
		}

		isCanceled := taskCtx.Err() == context.Canceled
		if isCanceled {
			message = "任务被取消"
		}

		err = ts.queueManager.CompleteTask(context.Background(), task.TaskID, ts.instanceID, success, message)
		if err != nil {
			logger.Error(taskCtx, "完成任务失败: %v", err)
		}
	}()
}

// taskCancelListener 任务取消监听器
func (ts *TaskScheduler) taskCancelListener(taskCtx context.Context, taskCancel context.CancelFunc, taskID string) {
	// 使用配置的取消检查间隔，提高响应速度
	checkInterval := time.Duration(ts.config.CancelCheckInterval) * time.Millisecond
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-taskCtx.Done():
			return
		case <-ticker.C:
			// 检查是否收到取消信号
			if ts.queueManager.IsTaskCancelled(context.Background(), taskID) {
				logger.Info(taskCtx, "收到任务取消信号: taskID=%s", taskID)
				taskCancel()
				return
			}
		}
	}
}

// executeDeployTask 执行部署任务
func (ts *TaskScheduler) executeDeployTask(ctx context.Context, task *types.DeployTask) error {
	// 更新任务进度
	updateProgress := func(progress int, message string) {
		task.Progress = progress
		task.Message = message
		// 这里可以更新到数据库或 Redis
		ts.queueManager.UpdateTaskStatus(ctx, task.TaskID, types.TaskStatusProcessing, message)
	}

	// 模拟部署进度
	updateProgress(10, "开始部署...")

	// 检查取消信号
	if ctx.Err() == context.Canceled {
		return ctx.Err()
	}

	updateProgress(30, "获取代码...")

	// 获取代码 tar 包
	tarData, err := ts.deployService.getCode(ctx, task.Request)
	if err != nil {
		return fmt.Errorf("获取代码失败: %v", err)
	}

	// 检查取消信号
	if ctx.Err() == context.Canceled {
		return ctx.Err()
	}

	updateProgress(50, "构建镜像...")

	// 构建和推送镜像
	imageURL, err := ts.deployService.buildService.BuildAndPush(ctx, task.Request.DeploymentID, tarData)
	if err != nil {
		return fmt.Errorf("构建镜像失败: %v", err)
	}

	// 检查取消信号
	if ctx.Err() == context.Canceled {
		return ctx.Err()
	}

	updateProgress(80, "部署到 K8s...")

	// 部署到 K8s
	startTime := task.CreatedAt
	err = ts.deployService.k8sService.Deploy(ctx, task.Request.DeploymentID, task.Request.ChatID, imageURL, task.UserInfo.ID, task.UserInfo.Email, startTime)
	if err != nil {
		// 设置失败状态并发送通知
		ts.deployService.setFailedStatusWithNotification(ctx, task.Request.DeploymentID, task.Request.ChatID, task.TraceID, fmt.Sprintf("K8s部署失败: %v", err), task.UserInfo, startTime)
		return fmt.Errorf("K8s部署失败: %v", err)
	}

	// 检查取消信号
	if ctx.Err() == context.Canceled {
		return ctx.Err()
	}

	updateProgress(100, "部署完成")

	// 设置成功状态并发送通知
	previewURL := fmt.Sprintf("https://%s.space.chatglm.site", task.Request.DeploymentID)
	successMessage := "部署成功"
	ts.deployService.setSuccessStatusWithNotification(ctx, task.Request.DeploymentID, task.Request.ChatID, task.TraceID, successMessage, imageURL, previewURL, task.UserInfo, startTime, tarData)

	return nil
}

// SubmitDeployTask 提交部署任务
func (ts *TaskScheduler) SubmitDeployTask(ctx context.Context, req *types.DeployRequest, userInfo *types.UserInfo, traceID string) (*types.DeployResponse, error) {
	// 生成任务ID
	taskID := generateTaskID()

	// 创建任务
	task := &types.DeployTask{
		TaskID:       taskID,
		DeploymentID: req.DeploymentID,
		Request:      req,
		UserInfo:     userInfo,
		TraceID:      traceID,
		Priority:     1, // 默认优先级
		CreatedAt:    time.Now(),
		Status:       types.TaskStatusPending,
		Message:      "任务已创建",
		Progress:     0,
	}

	// 提交任务到队列
	err := ts.queueManager.SubmitTask(ctx, task)
	if err != nil {
		return &types.DeployResponse{
			Success: false,
			Message: fmt.Sprintf("提交任务失败: %v", err),
		}, nil
	}

	// 创建数据库部署记录
	if ts.deployService.databaseService != nil {
		deployment := &Deployment{
			DeploymentID: req.DeploymentID,
			ChatID:       req.ChatID,
			UserID:       userInfo.ID,
			UserEmail:    userInfo.Email,
			Status:       "deploying",
			SubPath:      req.DeploymentID,
			TraceID:      traceID,
		}

		err := ts.deployService.databaseService.CreateDeployment(ctx, deployment)
		if err != nil {
			logger.Error(ctx, "创建部署记录失败: %v", err)
		}
	}

	// 发送开始部署通知
	if ts.deployService.notificationService != nil {
		startNotificationData := &NotificationData{
			DeploymentID: req.DeploymentID,
			ChatID:       req.ChatID,
			UserEmail:    userInfo.Email,
			UserID:       userInfo.ID,
			CodeTarURL:   req.CodeTarURL,
			Timestamp:    task.CreatedAt,
		}

		if err := ts.deployService.notificationService.SendDeployStartNotification(ctx, startNotificationData); err != nil {
			logger.Error(ctx, "发送开始部署通知失败: %v", err)
		}
	}

	return &types.DeployResponse{
		Success:      true,
		Message:      "任务已提交到队列，正在等待执行",
		DeploymentID: req.DeploymentID,
	}, nil
}

func generateTaskID() string {
	return fmt.Sprintf("task-%s-%d", uuid.New().String()[:8], time.Now().Unix())
}

// GetTaskDetails 获取任务详情（用于 API）
func (ts *TaskScheduler) GetTaskDetails(ctx context.Context, taskID string) (*types.DeployTask, error) {
	return ts.queueManager.GetTask(ctx, taskID)
}

// CancelTask 取消任务（用于 API）
func (ts *TaskScheduler) CancelTask(ctx context.Context, taskID string, reason string) error {
	return ts.queueManager.CancelTask(ctx, taskID, reason)
}

// ListTasks 列出任务（用于 API）
func (ts *TaskScheduler) ListTasks(ctx context.Context, req *types.TaskListRequest) (*types.TaskListResponse, error) {
	tasks, total, err := ts.queueManager.ListTasks(ctx, req)
	if err != nil {
		return &types.TaskListResponse{
			Success: false,
			Message: fmt.Sprintf("获取任务列表失败: %v", err),
		}, nil
	}

	return &types.TaskListResponse{
		Success: true,
		Message: "获取任务列表成功",
		Tasks:   tasks,
		Total:   total,
	}, nil
}

// GetQueueStats 获取队列统计（用于 API）
func (ts *TaskScheduler) GetQueueStats(ctx context.Context) (*types.QueueStatsResponse, error) {
	return ts.queueManager.GetQueueStats(ctx)
}

// ListInstances 获取实例列表（用于 API）
func (ts *TaskScheduler) ListInstances(ctx context.Context) (*types.InstanceListResponse, error) {
	instances, err := ts.queueManager.ListInstances(ctx)
	if err != nil {
		return &types.InstanceListResponse{
			Success: false,
			Message: fmt.Sprintf("获取实例列表失败: %v", err),
		}, nil
	}

	return &types.InstanceListResponse{
		Success:   true,
		Message:   "获取实例列表成功",
		Instances: instances,
	}, nil
}

// timeoutCleanupLoop 定期清理超时任务
func (ts *TaskScheduler) timeoutCleanupLoop() {
	defer ts.wg.Done()

	// 每5分钟清理一次超时任务
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	logger.Info(ts.ctx, "启动超时清理循环: instanceID=%s", ts.instanceID)

	for {
		select {
		case <-ts.ctx.Done():
			logger.Info(ts.ctx, "超时清理循环已停止: instanceID=%s", ts.instanceID)
			return
		case <-ticker.C:
			err := ts.queueManager.CleanupTimeoutTasks(ts.ctx)
			if err != nil {
				logger.Error(ts.ctx, "清理超时任务失败: instanceID=%s, error=%v", ts.instanceID, err)
			}
		}
	}
}
