package api

import (
	"code-deploy-service/internal/middleware"
	"code-deploy-service/internal/service"

	"github.com/gin-gonic/gin"
)

func SetupRoutes(deployService *service.DeployService) *gin.Engine {
	r := gin.Default()

	// 添加 trace-id 中间件
	r.Use(middleware.TraceIDMiddleware())

	r.GET("/", Index)
	handler := NewHandler(deployService)

	v1 := r.Group("/api/v1")
	{
		v1.POST("/deploy", handler.Deploy)
		v1.DELETE("/deploy", handler.Delete)
		v1.POST("/status", handler.GetStatus)
		v1.GET("/health", handler.Health)
		v1.GET("/deployments", handler.ListDeployments)
	}

	return r
}
