#!/bin/bash

# 部署状态监控脚本
# 用法: ./monitor_deployment.sh <deployment_id> [server_url]

DEPLOYMENT_ID=$1
SERVER_URL=${2:-"http://localhost:8080"}

if [ -z "$DEPLOYMENT_ID" ]; then
    echo "用法: $0 <deployment_id> [server_url]"
    echo "示例: $0 my-app-001 http://localhost:8080"
    exit 1
fi

echo "监控部署状态: $DEPLOYMENT_ID"
echo "服务器地址: $SERVER_URL"
echo "按 Ctrl+C 停止监控"
echo "----------------------------------------"

# 生成随机 trace-id
TRACE_ID="monitor-$(date +%s)-$$"

while true; do
    # 查询部署状态
    RESPONSE=$(curl -s -X POST "$SERVER_URL/api/v1/status" \
        -H "Content-Type: application/json" \
        -H "x-trace-id: $TRACE_ID" \
        -d "{\"deploymentId\": \"$DEPLOYMENT_ID\"}")
    
    # 检查 curl 是否成功
    if [ $? -ne 0 ]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ❌ 连接服务器失败"
        sleep 5
        continue
    fi
    
    # 解析响应
    SUCCESS=$(echo "$RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    STATUS=$(echo "$RESPONSE" | grep -o '"status":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    MESSAGE=$(echo "$RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    UPDATED_AT=$(echo "$RESPONSE" | grep -o '"updatedAt":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [ "$SUCCESS" = "true" ]; then
        case "$STATUS" in
            "pending")
                echo "[$TIMESTAMP] 🔄 部署进行中... (更新时间: $UPDATED_AT)"
                ;;
            "success")
                echo "[$TIMESTAMP] ✅ 部署成功! (完成时间: $UPDATED_AT)"
                echo "监控完成，部署已成功"
                exit 0
                ;;
            "failed")
                echo "[$TIMESTAMP] ❌ 部署失败: $MESSAGE (失败时间: $UPDATED_AT)"
                echo "监控完成，部署已失败"
                exit 1
                ;;
            *)
                echo "[$TIMESTAMP] ❓ 未知状态: $STATUS"
                ;;
        esac
    else
        echo "[$TIMESTAMP] ❌ 查询失败: $MESSAGE"
    fi
    
    # 等待 5 秒后再次查询
    sleep 5
done
